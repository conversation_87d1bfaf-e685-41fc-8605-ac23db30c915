{"ast": null, "code": "var _jsxFileName = \"C:\\\\Doc Maker AI\\\\indian-railways-reports\\\\src\\\\components\\\\SignatureCapture.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState } from 'react';\nimport SignatureCanvas from 'react-signature-canvas';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignatureCapture = ({\n  onSignatureSave,\n  onSignatureClear,\n  disabled = false,\n  className = ''\n}) => {\n  _s();\n  const signatureRef = useRef(null);\n  const [isEmpty, setIsEmpty] = useState(true);\n  const handleClear = () => {\n    if (signatureRef.current) {\n      signatureRef.current.clear();\n      setIsEmpty(true);\n      if (onSignatureClear) {\n        onSignatureClear();\n      }\n    }\n  };\n  const handleSave = () => {\n    if (signatureRef.current && !isEmpty) {\n      const dataUrl = signatureRef.current.toDataURL('image/png');\n      onSignatureSave(dataUrl);\n    }\n  };\n  const handleBegin = () => {\n    setIsEmpty(false);\n  };\n  const handleEnd = () => {\n    if (signatureRef.current) {\n      setIsEmpty(signatureRef.current.isEmpty());\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-4 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-2 border-gray-300 rounded-lg bg-white\",\n      children: /*#__PURE__*/_jsxDEV(SignatureCanvas, {\n        ref: signatureRef,\n        canvasProps: {\n          width: 500,\n          height: 200,\n          className: 'signature-canvas w-full cursor-crosshair'\n        },\n        onBegin: handleBegin,\n        onEnd: handleEnd,\n        backgroundColor: \"white\",\n        penColor: \"black\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600\",\n        children: isEmpty ? 'Please sign in the box above' : 'Signature captured'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleClear,\n          disabled: disabled || isEmpty,\n          className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-railway-blue disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleSave,\n          disabled: disabled || isEmpty,\n          className: \"px-4 py-2 text-sm font-medium text-white bg-railway-blue border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-railway-blue disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: \"Save Signature\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs text-gray-500 space-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u2022 Use your mouse or touch screen to sign\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u2022 Click \\\"Clear\\\" to start over\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u2022 Click \\\"Save Signature\\\" when finished\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(SignatureCapture, \"iZMQQ+4+STMNktwzMGHZBgL5Uts=\");\n_c = SignatureCapture;\nexport default SignatureCapture;\nvar _c;\n$RefreshReg$(_c, \"SignatureCapture\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "SignatureCanvas", "jsxDEV", "_jsxDEV", "SignatureCapture", "onSignatureSave", "onSignatureClear", "disabled", "className", "_s", "signatureRef", "isEmpty", "setIsEmpty", "handleClear", "current", "clear", "handleSave", "dataUrl", "toDataURL", "handleBegin", "handleEnd", "children", "ref", "canvasProps", "width", "height", "onBegin", "onEnd", "backgroundColor", "penColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Doc Maker AI/indian-railways-reports/src/components/SignatureCapture.tsx"], "sourcesContent": ["import React, { useRef, useState } from 'react';\nimport SignatureCanvas from 'react-signature-canvas';\n\ninterface SignatureCaptureProps {\n  onSignatureSave: (signatureDataUrl: string) => void;\n  onSignatureClear?: () => void;\n  disabled?: boolean;\n  className?: string;\n}\n\nconst SignatureCapture: React.FC<SignatureCaptureProps> = ({\n  onSignatureSave,\n  onSignatureClear,\n  disabled = false,\n  className = ''\n}) => {\n  const signatureRef = useRef<SignatureCanvas>(null);\n  const [isEmpty, setIsEmpty] = useState(true);\n\n  const handleClear = () => {\n    if (signatureRef.current) {\n      signatureRef.current.clear();\n      setIsEmpty(true);\n      if (onSignatureClear) {\n        onSignatureClear();\n      }\n    }\n  };\n\n  const handleSave = () => {\n    if (signatureRef.current && !isEmpty) {\n      const dataUrl = signatureRef.current.toDataURL('image/png');\n      onSignatureSave(dataUrl);\n    }\n  };\n\n  const handleBegin = () => {\n    setIsEmpty(false);\n  };\n\n  const handleEnd = () => {\n    if (signatureRef.current) {\n      setIsEmpty(signatureRef.current.isEmpty());\n    }\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <div className=\"border-2 border-gray-300 rounded-lg bg-white\">\n        <SignatureCanvas\n          ref={signatureRef}\n          canvasProps={{\n            width: 500,\n            height: 200,\n            className: 'signature-canvas w-full cursor-crosshair'\n          }}\n          onBegin={handleBegin}\n          onEnd={handleEnd}\n          backgroundColor=\"white\"\n          penColor=\"black\"\n        />\n      </div>\n      \n      <div className=\"flex justify-between items-center\">\n        <p className=\"text-sm text-gray-600\">\n          {isEmpty ? 'Please sign in the box above' : 'Signature captured'}\n        </p>\n        \n        <div className=\"flex space-x-3\">\n          <button\n            type=\"button\"\n            onClick={handleClear}\n            disabled={disabled || isEmpty}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-railway-blue disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Clear\n          </button>\n          \n          <button\n            type=\"button\"\n            onClick={handleSave}\n            disabled={disabled || isEmpty}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-railway-blue border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-railway-blue disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Save Signature\n          </button>\n        </div>\n      </div>\n      \n      {/* Instructions */}\n      <div className=\"text-xs text-gray-500 space-y-1\">\n        <p>• Use your mouse or touch screen to sign</p>\n        <p>• Click \"Clear\" to start over</p>\n        <p>• Click \"Save Signature\" when finished</p>\n      </div>\n    </div>\n  );\n};\n\nexport default SignatureCapture;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC/C,OAAOC,eAAe,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASrD,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,eAAe;EACfC,gBAAgB;EAChBC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,YAAY,GAAGX,MAAM,CAAkB,IAAI,CAAC;EAClD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIH,YAAY,CAACI,OAAO,EAAE;MACxBJ,YAAY,CAACI,OAAO,CAACC,KAAK,CAAC,CAAC;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIN,gBAAgB,EAAE;QACpBA,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC;EAED,MAAMU,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIN,YAAY,CAACI,OAAO,IAAI,CAACH,OAAO,EAAE;MACpC,MAAMM,OAAO,GAAGP,YAAY,CAACI,OAAO,CAACI,SAAS,CAAC,WAAW,CAAC;MAC3Db,eAAe,CAACY,OAAO,CAAC;IAC1B;EACF,CAAC;EAED,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBP,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMQ,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIV,YAAY,CAACI,OAAO,EAAE;MACxBF,UAAU,CAACF,YAAY,CAACI,OAAO,CAACH,OAAO,CAAC,CAAC,CAAC;IAC5C;EACF,CAAC;EAED,oBACER,OAAA;IAAKK,SAAS,EAAE,aAAaA,SAAS,EAAG;IAAAa,QAAA,gBACvClB,OAAA;MAAKK,SAAS,EAAC,8CAA8C;MAAAa,QAAA,eAC3DlB,OAAA,CAACF,eAAe;QACdqB,GAAG,EAAEZ,YAAa;QAClBa,WAAW,EAAE;UACXC,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXjB,SAAS,EAAE;QACb,CAAE;QACFkB,OAAO,EAAEP,WAAY;QACrBQ,KAAK,EAAEP,SAAU;QACjBQ,eAAe,EAAC,OAAO;QACvBC,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN9B,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAAa,QAAA,gBAChDlB,OAAA;QAAGK,SAAS,EAAC,uBAAuB;QAAAa,QAAA,EACjCV,OAAO,GAAG,8BAA8B,GAAG;MAAoB;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAEJ9B,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAa,QAAA,gBAC7BlB,OAAA;UACE+B,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEtB,WAAY;UACrBN,QAAQ,EAAEA,QAAQ,IAAII,OAAQ;UAC9BH,SAAS,EAAC,yOAAyO;UAAAa,QAAA,EACpP;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9B,OAAA;UACE+B,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEnB,UAAW;UACpBT,QAAQ,EAAEA,QAAQ,IAAII,OAAQ;UAC9BH,SAAS,EAAC,6OAA6O;UAAAa,QAAA,EACxP;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKK,SAAS,EAAC,iCAAiC;MAAAa,QAAA,gBAC9ClB,OAAA;QAAAkB,QAAA,EAAG;MAAwC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC/C9B,OAAA;QAAAkB,QAAA,EAAG;MAA6B;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpC9B,OAAA;QAAAkB,QAAA,EAAG;MAAsC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAvFIL,gBAAiD;AAAAgC,EAAA,GAAjDhC,gBAAiD;AAyFvD,eAAeA,gBAAgB;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}