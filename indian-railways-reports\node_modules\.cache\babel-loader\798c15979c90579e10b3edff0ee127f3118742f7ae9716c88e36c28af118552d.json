{"ast": null, "code": "var _jsxFileName = \"C:\\\\Doc Maker AI\\\\indian-railways-reports\\\\src\\\\components\\\\CreateReport.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\nimport SignatureCapture from './SignatureCapture';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateReport = () => {\n  _s();\n  const navigate = useNavigate();\n  const [templates, setTemplates] = useState([]);\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n  const [formData, setFormData] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [step, setStep] = useState('select');\n  const [reportId, setReportId] = useState(null);\n  useEffect(() => {\n    fetchTemplates();\n  }, []);\n  const fetchTemplates = async () => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.from('templates').select('*').order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      setTemplates(data || []);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to fetch templates');\n    }\n  };\n  const handleTemplateSelect = template => {\n    setSelectedTemplate(template);\n    setStep('form');\n\n    // Initialize form data with empty values for all placeholders\n    const initialData = {};\n    if (template.placeholder_map) {\n      Object.keys(template.placeholder_map).forEach(key => {\n        initialData[key] = '';\n      });\n    }\n    setFormData(initialData);\n  };\n  const handleInputChange = (key, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleFormSubmit = async e => {\n    e.preventDefault();\n    if (!selectedTemplate) return;\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Create report draft\n      const {\n        data: report,\n        error: reportError\n      } = await supabase.from('reports').insert({\n        template_id: selectedTemplate.id,\n        data: formData,\n        status: 'draft'\n      }).select().single();\n      if (reportError) throw reportError;\n      setReportId(report.id);\n      setStep('signature');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSignatureSave = async signatureDataUrl => {\n    if (!reportId) return;\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Convert data URL to blob\n      const response = await fetch(signatureDataUrl);\n      const blob = await response.blob();\n\n      // Upload signature\n      const fileName = `signature-${reportId}-${Date.now()}.png`;\n      const {\n        data: uploadData,\n        error: uploadError\n      } = await supabase.storage.from('signatures').upload(fileName, blob, {\n        contentType: 'image/png'\n      });\n      if (uploadError) throw uploadError;\n\n      // Get public URL\n      const {\n        data: {\n          publicUrl\n        }\n      } = supabase.storage.from('signatures').getPublicUrl(fileName);\n\n      // Update report with signature URL\n      const {\n        error: updateError\n      } = await supabase.from('reports').update({\n        signature_url: publicUrl\n      }).eq('id', reportId);\n      if (updateError) throw updateError;\n      setStep('complete');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to save signature');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateReport = async () => {\n    if (!reportId) return;\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Call generate report function\n      const response = await fetch(`https://lexnztbclzychlvrauhq.supabase.co/functions/v1/generate-report/${reportId}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to generate report');\n      }\n      const result = await response.json();\n\n      // Navigate to report view\n      navigate(`/reports/${reportId}`);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderTemplateSelection = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-semibold text-gray-900\",\n      children: \"Select Template\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), templates.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"No templates available. Please upload a template first.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/templates'),\n        className: \"mt-4 bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200\",\n        children: \"Manage Templates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: templates.map(template => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer\",\n        onClick: () => handleTemplateSelect(template),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: template.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500 mb-4\",\n          children: template.type.replace('_', ' ').toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: template.placeholder_map && Object.keys(template.placeholder_map).length > 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [Object.keys(template.placeholder_map).length, \" fields\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No fields detected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 15\n        }, this)]\n      }, template.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n  const renderForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-semibold text-gray-900\",\n        children: \"Fill Report Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setStep('select'),\n        className: \"text-railway-blue hover:text-blue-800\",\n        children: \"\\u2190 Back to Templates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold mb-4\",\n        children: [\"Template: \", selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleFormSubmit,\n        className: \"space-y-4\",\n        children: [(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.placeholder_map) && Object.entries(selectedTemplate.placeholder_map).map(([key, label]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), key === 'Date' ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"date\",\n            value: formData[key] || '',\n            onChange: e => handleInputChange(key, e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this) : key === 'Time' ? /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"time\",\n            value: formData[key] || '',\n            onChange: e => handleInputChange(key, e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: formData[key] || '',\n            onChange: e => handleInputChange(key, e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\",\n            placeholder: `Enter ${label.toLowerCase()}`,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading,\n          className: \"w-full bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg transition-colors duration-200\",\n          children: loading ? 'Creating...' : 'Continue to Signature'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n  const renderSignature = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-semibold text-gray-900\",\n        children: \"Add Digital Signature\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setStep('form'),\n        className: \"text-railway-blue hover:text-blue-800\",\n        children: \"\\u2190 Back to Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please sign in the box below:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignatureCapture, {\n          onSignatureSave: handleSignatureSave,\n          disabled: loading,\n          className: \"w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n  const renderComplete = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-semibold text-gray-900\",\n      children: \"Report Created Successfully\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg p-6 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-green-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"Report Draft Created\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Your report has been saved with signature. You can now generate the final document.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          className: \"bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200\",\n          children: \"Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: generateReport,\n          disabled: loading,\n          className: \"bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200\",\n          children: loading ? 'Generating...' : 'Generate Report'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 9\n    }, this), step === 'select' && renderTemplateSelection(), step === 'form' && renderForm(), step === 'signature' && renderSignature(), step === 'complete' && renderComplete()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateReport, \"mYmaVMocwbeIh+t+29OHSswZzQA=\", false, function () {\n  return [useNavigate];\n});\n_c = CreateReport;\nexport default CreateReport;\nvar _c;\n$RefreshReg$(_c, \"CreateReport\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "supabase", "SignatureCapture", "jsxDEV", "_jsxDEV", "CreateReport", "_s", "navigate", "templates", "setTemplates", "selectedTemplate", "setSelectedTemplate", "formData", "setFormData", "loading", "setLoading", "error", "setError", "step", "setStep", "reportId", "setReportId", "fetchTemplates", "data", "from", "select", "order", "ascending", "err", "Error", "message", "handleTemplateSelect", "template", "initialData", "placeholder_map", "Object", "keys", "for<PERSON>ach", "key", "handleInputChange", "value", "prev", "handleFormSubmit", "e", "preventDefault", "report", "reportError", "insert", "template_id", "id", "status", "single", "handleSignatureSave", "signatureDataUrl", "response", "fetch", "blob", "fileName", "Date", "now", "uploadData", "uploadError", "storage", "upload", "contentType", "publicUrl", "getPublicUrl", "updateError", "update", "signature_url", "eq", "generateReport", "method", "headers", "ok", "result", "json", "renderTemplateSelection", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "name", "type", "replace", "toUpperCase", "renderForm", "onSubmit", "entries", "label", "onChange", "target", "required", "placeholder", "toLowerCase", "disabled", "renderSignature", "onSignatureSave", "renderComplete", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Doc Maker AI/indian-railways-reports/src/components/CreateReport.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { supabase, Template, Report } from '../lib/supabase';\nimport SignatureCapture from './SignatureCapture';\n\nconst CreateReport: React.FC = () => {\n  const navigate = useNavigate();\n  \n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);\n  const [formData, setFormData] = useState<Record<string, string>>({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [step, setStep] = useState<'select' | 'form' | 'signature' | 'complete'>('select');\n  const [reportId, setReportId] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchTemplates();\n  }, []);\n\n  const fetchTemplates = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('templates')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      setTemplates(data || []);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to fetch templates');\n    }\n  };\n\n  const handleTemplateSelect = (template: Template) => {\n    setSelectedTemplate(template);\n    setStep('form');\n    \n    // Initialize form data with empty values for all placeholders\n    const initialData: Record<string, string> = {};\n    if (template.placeholder_map) {\n      Object.keys(template.placeholder_map).forEach(key => {\n        initialData[key] = '';\n      });\n    }\n    setFormData(initialData);\n  };\n\n  const handleInputChange = (key: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleFormSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!selectedTemplate) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Create report draft\n      const { data: report, error: reportError } = await supabase\n        .from('reports')\n        .insert({\n          template_id: selectedTemplate.id,\n          data: formData,\n          status: 'draft'\n        })\n        .select()\n        .single();\n\n      if (reportError) throw reportError;\n\n      setReportId(report.id);\n      setStep('signature');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSignatureSave = async (signatureDataUrl: string) => {\n    if (!reportId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Convert data URL to blob\n      const response = await fetch(signatureDataUrl);\n      const blob = await response.blob();\n\n      // Upload signature\n      const fileName = `signature-${reportId}-${Date.now()}.png`;\n      const { data: uploadData, error: uploadError } = await supabase.storage\n        .from('signatures')\n        .upload(fileName, blob, {\n          contentType: 'image/png'\n        });\n\n      if (uploadError) throw uploadError;\n\n      // Get public URL\n      const { data: { publicUrl } } = supabase.storage\n        .from('signatures')\n        .getPublicUrl(fileName);\n\n      // Update report with signature URL\n      const { error: updateError } = await supabase\n        .from('reports')\n        .update({ signature_url: publicUrl })\n        .eq('id', reportId);\n\n      if (updateError) throw updateError;\n\n      setStep('complete');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to save signature');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateReport = async () => {\n    if (!reportId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Call generate report function\n      const response = await fetch(`https://lexnztbclzychlvrauhq.supabase.co/functions/v1/generate-report/${reportId}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate report');\n      }\n\n      const result = await response.json();\n      \n      // Navigate to report view\n      navigate(`/reports/${reportId}`);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const renderTemplateSelection = () => (\n    <div className=\"space-y-6\">\n      <h2 className=\"text-2xl font-semibold text-gray-900\">Select Template</h2>\n      \n      {templates.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">No templates available. Please upload a template first.</p>\n          <button\n            onClick={() => navigate('/templates')}\n            className=\"mt-4 bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200\"\n          >\n            Manage Templates\n          </button>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {templates.map((template) => (\n            <div\n              key={template.id}\n              className=\"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer\"\n              onClick={() => handleTemplateSelect(template)}\n            >\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {template.name}\n              </h3>\n              <p className=\"text-sm text-gray-500 mb-4\">\n                {template.type.replace('_', ' ').toUpperCase()}\n              </p>\n              <div className=\"text-sm text-gray-600\">\n                {template.placeholder_map && Object.keys(template.placeholder_map).length > 0 ? (\n                  <p>{Object.keys(template.placeholder_map).length} fields</p>\n                ) : (\n                  <p>No fields detected</p>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n\n  const renderForm = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-semibold text-gray-900\">Fill Report Details</h2>\n        <button\n          onClick={() => setStep('select')}\n          className=\"text-railway-blue hover:text-blue-800\"\n        >\n          ← Back to Templates\n        </button>\n      </div>\n      \n      <div className=\"bg-white shadow-lg rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Template: {selectedTemplate?.name}</h3>\n        \n        <form onSubmit={handleFormSubmit} className=\"space-y-4\">\n          {selectedTemplate?.placeholder_map && Object.entries(selectedTemplate.placeholder_map).map(([key, label]) => (\n            <div key={key}>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {label}\n              </label>\n              {key === 'Date' ? (\n                <input\n                  type=\"date\"\n                  value={formData[key] || ''}\n                  onChange={(e) => handleInputChange(key, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\"\n                  required\n                />\n              ) : key === 'Time' ? (\n                <input\n                  type=\"time\"\n                  value={formData[key] || ''}\n                  onChange={(e) => handleInputChange(key, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\"\n                  required\n                />\n              ) : (\n                <input\n                  type=\"text\"\n                  value={formData[key] || ''}\n                  onChange={(e) => handleInputChange(key, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\"\n                  placeholder={`Enter ${label.toLowerCase()}`}\n                  required\n                />\n              )}\n            </div>\n          ))}\n          \n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg transition-colors duration-200\"\n          >\n            {loading ? 'Creating...' : 'Continue to Signature'}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n\n  const renderSignature = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-semibold text-gray-900\">Add Digital Signature</h2>\n        <button\n          onClick={() => setStep('form')}\n          className=\"text-railway-blue hover:text-blue-800\"\n        >\n          ← Back to Form\n        </button>\n      </div>\n      \n      <div className=\"bg-white shadow-lg rounded-lg p-6\">\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-600\">Please sign in the box below:</p>\n\n          <SignatureCapture\n            onSignatureSave={handleSignatureSave}\n            disabled={loading}\n            className=\"w-full\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderComplete = () => (\n    <div className=\"space-y-6\">\n      <h2 className=\"text-2xl font-semibold text-gray-900\">Report Created Successfully</h2>\n      \n      <div className=\"bg-white shadow-lg rounded-lg p-6 text-center\">\n        <div className=\"mb-6\">\n          <div className=\"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4\">\n            <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Report Draft Created</h3>\n          <p className=\"text-gray-600\">Your report has been saved with signature. You can now generate the final document.</p>\n        </div>\n        \n        <div className=\"flex justify-center space-x-4\">\n          <button\n            onClick={() => navigate('/')}\n            className=\"bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200\"\n          >\n            Back to Dashboard\n          </button>\n          <button\n            onClick={generateReport}\n            disabled={loading}\n            className=\"bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200\"\n          >\n            {loading ? 'Generating...' : 'Generate Report'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      {error && (\n        <div className=\"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n          {error}\n        </div>\n      )}\n\n      {step === 'select' && renderTemplateSelection()}\n      {step === 'form' && renderForm()}\n      {step === 'signature' && renderSignature()}\n      {step === 'complete' && renderComplete()}\n    </div>\n  );\n};\n\nexport default CreateReport;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAA0B,iBAAiB;AAC5D,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGb,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAyB,CAAC,CAAC,CAAC;EACpE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAA+C,QAAQ,CAAC;EACxF,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACduB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAM;QAAEC,IAAI;QAAEP;MAAM,CAAC,GAAG,MAAMf,QAAQ,CACnCuB,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIX,KAAK,EAAE,MAAMA,KAAK;MACtBP,YAAY,CAACc,IAAI,IAAI,EAAE,CAAC;IAC1B,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,2BAA2B,CAAC;IAC5E;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAIC,QAAkB,IAAK;IACnDrB,mBAAmB,CAACqB,QAAQ,CAAC;IAC7Bb,OAAO,CAAC,MAAM,CAAC;;IAEf;IACA,MAAMc,WAAmC,GAAG,CAAC,CAAC;IAC9C,IAAID,QAAQ,CAACE,eAAe,EAAE;MAC5BC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,eAAe,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;QACnDL,WAAW,CAACK,GAAG,CAAC,GAAG,EAAE;MACvB,CAAC,CAAC;IACJ;IACAzB,WAAW,CAACoB,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMM,iBAAiB,GAAGA,CAACD,GAAW,EAAEE,KAAa,KAAK;IACxD3B,WAAW,CAAC4B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,GAAG,GAAGE;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAOC,CAAkB,IAAK;IACrDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAClC,gBAAgB,EAAE;IAEvB,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM;QAAEM,IAAI,EAAEsB,MAAM;QAAE7B,KAAK,EAAE8B;MAAY,CAAC,GAAG,MAAM7C,QAAQ,CACxDuB,IAAI,CAAC,SAAS,CAAC,CACfuB,MAAM,CAAC;QACNC,WAAW,EAAEtC,gBAAgB,CAACuC,EAAE;QAChC1B,IAAI,EAAEX,QAAQ;QACdsC,MAAM,EAAE;MACV,CAAC,CAAC,CACDzB,MAAM,CAAC,CAAC,CACR0B,MAAM,CAAC,CAAC;MAEX,IAAIL,WAAW,EAAE,MAAMA,WAAW;MAElCzB,WAAW,CAACwB,MAAM,CAACI,EAAE,CAAC;MACtB9B,OAAO,CAAC,WAAW,CAAC;IACtB,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,yBAAyB,CAAC;IAC1E,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,mBAAmB,GAAG,MAAOC,gBAAwB,IAAK;IAC9D,IAAI,CAACjC,QAAQ,EAAE;IAEf,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMqC,QAAQ,GAAG,MAAMC,KAAK,CAACF,gBAAgB,CAAC;MAC9C,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACE,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMC,QAAQ,GAAG,aAAarC,QAAQ,IAAIsC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;MAC1D,MAAM;QAAEpC,IAAI,EAAEqC,UAAU;QAAE5C,KAAK,EAAE6C;MAAY,CAAC,GAAG,MAAM5D,QAAQ,CAAC6D,OAAO,CACpEtC,IAAI,CAAC,YAAY,CAAC,CAClBuC,MAAM,CAACN,QAAQ,EAAED,IAAI,EAAE;QACtBQ,WAAW,EAAE;MACf,CAAC,CAAC;MAEJ,IAAIH,WAAW,EAAE,MAAMA,WAAW;;MAElC;MACA,MAAM;QAAEtC,IAAI,EAAE;UAAE0C;QAAU;MAAE,CAAC,GAAGhE,QAAQ,CAAC6D,OAAO,CAC7CtC,IAAI,CAAC,YAAY,CAAC,CAClB0C,YAAY,CAACT,QAAQ,CAAC;;MAEzB;MACA,MAAM;QAAEzC,KAAK,EAAEmD;MAAY,CAAC,GAAG,MAAMlE,QAAQ,CAC1CuB,IAAI,CAAC,SAAS,CAAC,CACf4C,MAAM,CAAC;QAAEC,aAAa,EAAEJ;MAAU,CAAC,CAAC,CACpCK,EAAE,CAAC,IAAI,EAAElD,QAAQ,CAAC;MAErB,IAAI+C,WAAW,EAAE,MAAMA,WAAW;MAElChD,OAAO,CAAC,UAAU,CAAC;IACrB,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,0BAA0B,CAAC;IAC3E,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwD,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnD,QAAQ,EAAE;IAEf,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMqC,QAAQ,GAAG,MAAMC,KAAK,CAAC,yEAAyEnC,QAAQ,EAAE,EAAE;QAChHoD,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,yNAAyN;UAC1O,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACnB,QAAQ,CAACoB,EAAE,EAAE;QAChB,MAAM,IAAI7C,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAM8C,MAAM,GAAG,MAAMrB,QAAQ,CAACsB,IAAI,CAAC,CAAC;;MAEpC;MACArE,QAAQ,CAAC,YAAYa,QAAQ,EAAE,CAAC;IAClC,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,2BAA2B,CAAC;IAC5E,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAM8D,uBAAuB,GAAGA,CAAA,kBAC9BzE,OAAA;IAAK0E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3E,OAAA;MAAI0E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAe;MAAAtB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAExE1E,SAAS,CAAC2E,MAAM,KAAK,CAAC,gBACrB/E,OAAA;MAAK0E,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B3E,OAAA;QAAG0E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAuD;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACxF9E,OAAA;QACEgF,OAAO,EAAEA,CAAA,KAAM7E,QAAQ,CAAC,YAAY,CAAE;QACtCuE,SAAS,EAAC,uGAAuG;QAAAC,QAAA,EAClH;MAED;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAEN9E,OAAA;MAAK0E,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEvE,SAAS,CAAC6E,GAAG,CAAErD,QAAQ,iBACtB5B,OAAA;QAEE0E,SAAS,EAAC,8GAA8G;QACxHM,OAAO,EAAEA,CAAA,KAAMrD,oBAAoB,CAACC,QAAQ,CAAE;QAAA+C,QAAA,gBAE9C3E,OAAA;UAAI0E,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EACrD/C,QAAQ,CAACsD;QAAI;UAAA7B,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACL9E,OAAA;UAAG0E,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtC/C,QAAQ,CAACuD,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;QAAC;UAAAhC,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACJ9E,OAAA;UAAK0E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACnC/C,QAAQ,CAACE,eAAe,IAAIC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,eAAe,CAAC,CAACiD,MAAM,GAAG,CAAC,gBAC3E/E,OAAA;YAAA2E,QAAA,GAAI5C,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACE,eAAe,CAAC,CAACiD,MAAM,EAAC,SAAO;UAAA;YAAA1B,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAE5D9E,OAAA;YAAA2E,QAAA,EAAG;UAAkB;YAAAtB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACzB;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GAhBDlD,QAAQ,CAACiB,EAAE;QAAAQ,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBb,CACN;IAAC;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAzB,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMQ,UAAU,GAAGA,CAAA,kBACjBtF,OAAA;IAAK0E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3E,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3E,OAAA;QAAI0E,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAmB;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7E9E,OAAA;QACEgF,OAAO,EAAEA,CAAA,KAAMjE,OAAO,CAAC,QAAQ,CAAE;QACjC2D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAClD;MAED;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN9E,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3E,OAAA;QAAI0E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,GAAC,YAAU,EAACrE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE4E,IAAI;MAAA;QAAA7B,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAElF9E,OAAA;QAAMuF,QAAQ,EAAEjD,gBAAiB;QAACoC,SAAS,EAAC,WAAW;QAAAC,QAAA,GACpD,CAAArE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwB,eAAe,KAAIC,MAAM,CAACyD,OAAO,CAAClF,gBAAgB,CAACwB,eAAe,CAAC,CAACmD,GAAG,CAAC,CAAC,CAAC/C,GAAG,EAAEuD,KAAK,CAAC,kBACtGzF,OAAA;UAAA2E,QAAA,gBACE3E,OAAA;YAAO0E,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAC5Dc;UAAK;YAAApC,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EACP5C,GAAG,KAAK,MAAM,gBACblC,OAAA;YACEmF,IAAI,EAAC,MAAM;YACX/C,KAAK,EAAE5B,QAAQ,CAAC0B,GAAG,CAAC,IAAI,EAAG;YAC3BwD,QAAQ,EAAGnD,CAAC,IAAKJ,iBAAiB,CAACD,GAAG,EAAEK,CAAC,CAACoD,MAAM,CAACvD,KAAK,CAAE;YACxDsC,SAAS,EAAC,4GAA4G;YACtHkB,QAAQ;UAAA;YAAAvC,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,GACA5C,GAAG,KAAK,MAAM,gBAChBlC,OAAA;YACEmF,IAAI,EAAC,MAAM;YACX/C,KAAK,EAAE5B,QAAQ,CAAC0B,GAAG,CAAC,IAAI,EAAG;YAC3BwD,QAAQ,EAAGnD,CAAC,IAAKJ,iBAAiB,CAACD,GAAG,EAAEK,CAAC,CAACoD,MAAM,CAACvD,KAAK,CAAE;YACxDsC,SAAS,EAAC,4GAA4G;YACtHkB,QAAQ;UAAA;YAAAvC,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,gBAEF9E,OAAA;YACEmF,IAAI,EAAC,MAAM;YACX/C,KAAK,EAAE5B,QAAQ,CAAC0B,GAAG,CAAC,IAAI,EAAG;YAC3BwD,QAAQ,EAAGnD,CAAC,IAAKJ,iBAAiB,CAACD,GAAG,EAAEK,CAAC,CAACoD,MAAM,CAACvD,KAAK,CAAE;YACxDsC,SAAS,EAAC,4GAA4G;YACtHmB,WAAW,EAAE,SAASJ,KAAK,CAACK,WAAW,CAAC,CAAC,EAAG;YAC5CF,QAAQ;UAAA;YAAAvC,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACF;QAAA,GA7BO5C,GAAG;UAAAmB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BR,CACN,CAAC,eAEF9E,OAAA;UACEmF,IAAI,EAAC,QAAQ;UACbY,QAAQ,EAAErF,OAAQ;UAClBgE,SAAS,EAAC,8HAA8H;UAAAC,QAAA,EAEvIjE,OAAO,GAAG,aAAa,GAAG;QAAuB;UAAA2C,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAzB,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMkB,eAAe,GAAGA,CAAA,kBACtBhG,OAAA;IAAK0E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3E,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3E,OAAA;QAAI0E,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAqB;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/E9E,OAAA;QACEgF,OAAO,EAAEA,CAAA,KAAMjE,OAAO,CAAC,MAAM,CAAE;QAC/B2D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAClD;MAED;QAAAtB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN9E,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChD3E,OAAA;QAAK0E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3E,OAAA;UAAG0E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6B;UAAAtB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAE9D9E,OAAA,CAACF,gBAAgB;UACfmG,eAAe,EAAEjD,mBAAoB;UACrC+C,QAAQ,EAAErF,OAAQ;UAClBgE,SAAS,EAAC;QAAQ;UAAArB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAzB,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMoB,cAAc,GAAGA,CAAA,kBACrBlG,OAAA;IAAK0E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3E,OAAA;MAAI0E,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAA2B;MAAAtB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErF9E,OAAA;MAAK0E,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5D3E,OAAA;QAAK0E,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3E,OAAA;UAAK0E,SAAS,EAAC,mFAAmF;UAAAC,QAAA,eAChG3E,OAAA;YAAK0E,SAAS,EAAC,wBAAwB;YAACyB,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA1B,QAAA,eAC3F3E,OAAA;cAAMsG,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAApD,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF;QAAC;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9E,OAAA;UAAI0E,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAoB;UAAAtB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClF9E,OAAA;UAAG0E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmF;UAAAtB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CAAC,eAEN9E,OAAA;QAAK0E,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5C3E,OAAA;UACEgF,OAAO,EAAEA,CAAA,KAAM7E,QAAQ,CAAC,GAAG,CAAE;UAC7BuE,SAAS,EAAC,8FAA8F;UAAAC,QAAA,EACzG;QAED;UAAAtB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9E,OAAA;UACEgF,OAAO,EAAEb,cAAe;UACxB4B,QAAQ,EAAErF,OAAQ;UAClBgE,SAAS,EAAC,uHAAuH;UAAAC,QAAA,EAEhIjE,OAAO,GAAG,eAAe,GAAG;QAAiB;UAAA2C,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAzB,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE9E,OAAA;IAAK0E,SAAS,EAAC,mBAAmB;IAAAC,QAAA,GAC/B/D,KAAK,iBACJZ,OAAA;MAAK0E,SAAS,EAAC,sEAAsE;MAAAC,QAAA,EAClF/D;IAAK;MAAAyC,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAhE,IAAI,KAAK,QAAQ,IAAI2D,uBAAuB,CAAC,CAAC,EAC9C3D,IAAI,KAAK,MAAM,IAAIwE,UAAU,CAAC,CAAC,EAC/BxE,IAAI,KAAK,WAAW,IAAIkF,eAAe,CAAC,CAAC,EACzClF,IAAI,KAAK,UAAU,IAAIoF,cAAc,CAAC,CAAC;EAAA;IAAA7C,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrC,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA9UID,YAAsB;EAAA,QACTL,WAAW;AAAA;AAAA8G,EAAA,GADxBzG,YAAsB;AAgV5B,eAAeA,YAAY;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}