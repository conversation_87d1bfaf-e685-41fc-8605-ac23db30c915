{"ast": null, "code": "var _jsxFileName = \"C:\\\\Doc Maker AI\\\\indian-railways-reports\\\\src\\\\components\\\\ReportView.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportView = () => {\n  _s();\n  var _templates, _templates2, _templates2$type;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [report, setReport] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [generating, setGenerating] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (id) {\n      fetchReport(id);\n    }\n  }, [id]);\n  const fetchReport = async reportId => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.from('reports').select(`\n          *,\n          templates (\n            name,\n            type\n          )\n        `).eq('id', reportId).single();\n      if (error) throw error;\n      setReport(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to fetch report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateReport = async () => {\n    if (!report) return;\n    try {\n      setGenerating(true);\n      setError(null);\n      const response = await fetch(`https://lexnztbclzychlvrauhq.supabase.co/functions/v1/generate-report/${report.id}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to generate report');\n      }\n      const result = await response.json();\n      setReport(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate report');\n    } finally {\n      setGenerating(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-railway-blue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this);\n  }\n  if (!report) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Report not found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"mt-4 bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200\",\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Report Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-1\",\n          children: [\"Template: \", (_templates = report.templates) === null || _templates === void 0 ? void 0 : _templates.name, \" | Type: \", (_templates2 = report.templates) === null || _templates2 === void 0 ? void 0 : (_templates2$type = _templates2.type) === null || _templates2$type === void 0 ? void 0 : _templates2$type.replace('_', ' ').toUpperCase()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"text-railway-blue hover:text-blue-800\",\n        children: \"\\u2190 Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-lg rounded-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-4\",\n          children: \"Report Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: Object.entries(report.data).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-700\",\n              children: [key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()), \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-900\",\n              children: value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${report.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`,\n              children: report.status.toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm text-gray-500 mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Created:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: new Date(report.created_at).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-lg rounded-lg p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-4\",\n          children: \"Digital Signature\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), report.signature_url ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: report.signature_url,\n            alt: \"Digital Signature\",\n            className: \"max-w-full h-auto border border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 text-gray-500\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No signature available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-4\",\n        children: \"Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), report.status === 'draft' ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"Generate the final document to download PDF and DOCX files.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: generateReport,\n          disabled: generating,\n          className: \"bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200\",\n          children: generating ? 'Generating...' : 'Generate Report'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center space-x-4\",\n        children: [report.generated_pdf_url && /*#__PURE__*/_jsxDEV(\"a\", {\n          href: report.generated_pdf_url,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"bg-railway-orange hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 inline-block\",\n          children: \"Download PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 15\n        }, this), report.generated_doc_url && /*#__PURE__*/_jsxDEV(\"a\", {\n          href: report.generated_doc_url,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200 inline-block\",\n          children: \"Download DOCX\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), report.status === 'generated' && report.generated_pdf_url && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900 mb-4\",\n        children: \"Document Preview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-96 border border-gray-300 rounded\",\n        children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n          src: report.generated_pdf_url,\n          className: \"w-full h-full rounded\",\n          title: \"Report Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportView, \"K/iDClzzO989wPbQbyeydheIkX0=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ReportView;\nexport default ReportView;\nvar _c;\n$RefreshReg$(_c, \"ReportView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "supabase", "jsxDEV", "_jsxDEV", "ReportView", "_s", "_templates", "_templates2", "_templates2$type", "id", "navigate", "report", "setReport", "loading", "setLoading", "generating", "setGenerating", "error", "setError", "fetchReport", "reportId", "data", "from", "select", "eq", "single", "err", "Error", "message", "generateReport", "response", "fetch", "method", "headers", "ok", "result", "json", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "templates", "name", "type", "replace", "toUpperCase", "Object", "entries", "map", "key", "value", "l", "status", "Date", "created_at", "toLocaleString", "signature_url", "src", "alt", "disabled", "generated_pdf_url", "href", "target", "rel", "generated_doc_url", "title", "_c", "$RefreshReg$"], "sources": ["C:/Doc Maker AI/indian-railways-reports/src/components/ReportView.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { supabase, Report } from '../lib/supabase';\n\nconst ReportView: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  \n  const [report, setReport] = useState<Report | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [generating, setGenerating] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (id) {\n      fetchReport(id);\n    }\n  }, [id]);\n\n  const fetchReport = async (reportId: string) => {\n    try {\n      setLoading(true);\n      const { data, error } = await supabase\n        .from('reports')\n        .select(`\n          *,\n          templates (\n            name,\n            type\n          )\n        `)\n        .eq('id', reportId)\n        .single();\n\n      if (error) throw error;\n      setReport(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to fetch report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateReport = async () => {\n    if (!report) return;\n\n    try {\n      setGenerating(true);\n      setError(null);\n\n      const response = await fetch(`https://lexnztbclzychlvrauhq.supabase.co/functions/v1/generate-report/${report.id}`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate report');\n      }\n\n      const result = await response.json();\n      setReport(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate report');\n    } finally {\n      setGenerating(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-railway-blue\"></div>\n      </div>\n    );\n  }\n\n  if (!report) {\n    return (\n      <div className=\"text-center py-8\">\n        <p className=\"text-gray-500\">Report not found</p>\n        <button\n          onClick={() => navigate('/')}\n          className=\"mt-4 bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200\"\n        >\n          Back to Dashboard\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Report Details</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Template: {(report as any).templates?.name} | \n            Type: {(report as any).templates?.type?.replace('_', ' ').toUpperCase()}\n          </p>\n        </div>\n        <button\n          onClick={() => navigate('/')}\n          className=\"text-railway-blue hover:text-blue-800\"\n        >\n          ← Back to Dashboard\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n          {error}\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Report Data */}\n        <div className=\"bg-white shadow-lg rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Report Data</h2>\n          <div className=\"space-y-3\">\n            {Object.entries(report.data).map(([key, value]) => (\n              <div key={key} className=\"flex justify-between\">\n                <span className=\"font-medium text-gray-700\">\n                  {key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}:\n                </span>\n                <span className=\"text-gray-900\">{value as string}</span>\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"mt-6 pt-4 border-t border-gray-200\">\n            <div className=\"flex justify-between text-sm text-gray-500\">\n              <span>Status:</span>\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                report.status === 'draft' \n                  ? 'bg-yellow-100 text-yellow-800' \n                  : 'bg-green-100 text-green-800'\n              }`}>\n                {report.status.toUpperCase()}\n              </span>\n            </div>\n            <div className=\"flex justify-between text-sm text-gray-500 mt-2\">\n              <span>Created:</span>\n              <span>{new Date(report.created_at).toLocaleString()}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Signature */}\n        <div className=\"bg-white shadow-lg rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Digital Signature</h2>\n          {report.signature_url ? (\n            <div className=\"text-center\">\n              <img\n                src={report.signature_url}\n                alt=\"Digital Signature\"\n                className=\"max-w-full h-auto border border-gray-300 rounded\"\n              />\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-gray-500\">\n              <p>No signature available</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Actions */}\n      <div className=\"bg-white shadow-lg rounded-lg p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Actions</h2>\n        \n        {report.status === 'draft' ? (\n          <div className=\"text-center py-4\">\n            <p className=\"text-gray-600 mb-4\">Generate the final document to download PDF and DOCX files.</p>\n            <button\n              onClick={generateReport}\n              disabled={generating}\n              className=\"bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200\"\n            >\n              {generating ? 'Generating...' : 'Generate Report'}\n            </button>\n          </div>\n        ) : (\n          <div className=\"flex justify-center space-x-4\">\n            {report.generated_pdf_url && (\n              <a\n                href={report.generated_pdf_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"bg-railway-orange hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 inline-block\"\n              >\n                Download PDF\n              </a>\n            )}\n            {report.generated_doc_url && (\n              <a\n                href={report.generated_doc_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200 inline-block\"\n              >\n                Download DOCX\n              </a>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* PDF Preview */}\n      {report.status === 'generated' && report.generated_pdf_url && (\n        <div className=\"bg-white shadow-lg rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Document Preview</h2>\n          <div className=\"w-full h-96 border border-gray-300 rounded\">\n            <iframe\n              src={report.generated_pdf_url}\n              className=\"w-full h-full rounded\"\n              title=\"Report Preview\"\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,QAAQ,QAAgB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,gBAAA;EACjC,MAAM;IAAEC;EAAG,CAAC,GAAGV,SAAS,CAAiB,CAAC;EAC1C,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAIW,EAAE,EAAE;MACNU,WAAW,CAACV,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAER,MAAMU,WAAW,GAAG,MAAOC,QAAgB,IAAK;IAC9C,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM;QAAEO,IAAI;QAAEJ;MAAM,CAAC,GAAG,MAAMhB,QAAQ,CACnCqB,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CACDC,EAAE,CAAC,IAAI,EAAEJ,QAAQ,CAAC,CAClBK,MAAM,CAAC,CAAC;MAEX,IAAIR,KAAK,EAAE,MAAMA,KAAK;MACtBL,SAAS,CAACS,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZR,QAAQ,CAACQ,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,wBAAwB,CAAC;IACzE,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAClB,MAAM,EAAE;IAEb,IAAI;MACFK,aAAa,CAAC,IAAI,CAAC;MACnBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAAC,yEAAyEpB,MAAM,CAACF,EAAE,EAAE,EAAE;QACjHuB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,yNAAyN;UAC1O,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIP,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA,MAAMQ,MAAM,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCxB,SAAS,CAACuB,MAAM,CAAC;IACnB,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZR,QAAQ,CAACQ,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,2BAA2B,CAAC;IAC5E,CAAC,SAAS;MACRZ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACEV,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDnC,OAAA;QAAKkC,SAAS,EAAC;MAAoE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC;EAEV;EAEA,IAAI,CAAC/B,MAAM,EAAE;IACX,oBACER,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BnC,OAAA;QAAGkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACjDvC,OAAA;QACEwC,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,GAAG,CAAE;QAC7B2B,SAAS,EAAC,uGAAuG;QAAAC,QAAA,EAClH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEvC,OAAA;IAAKkC,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1CnC,OAAA;MAAKkC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnC,OAAA;QAAAmC,QAAA,gBACEnC,OAAA;UAAIkC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEvC,OAAA;UAAGkC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,YACtB,GAAAhC,UAAA,GAAEK,MAAM,CAASiC,SAAS,cAAAtC,UAAA,uBAAzBA,UAAA,CAA2BuC,IAAI,EAAC,WACrC,GAAAtC,WAAA,GAAEI,MAAM,CAASiC,SAAS,cAAArC,WAAA,wBAAAC,gBAAA,GAAzBD,WAAA,CAA2BuC,IAAI,cAAAtC,gBAAA,uBAA/BA,gBAAA,CAAiCuC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNvC,OAAA;QACEwC,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,GAAG,CAAE;QAC7B2B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAClD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELzB,KAAK,iBACJd,OAAA;MAAKkC,SAAS,EAAC,iEAAiE;MAAAC,QAAA,EAC7ErB;IAAK;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDvC,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDnC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnC,OAAA;UAAIkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEvC,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBW,MAAM,CAACC,OAAO,CAACvC,MAAM,CAACU,IAAI,CAAC,CAAC8B,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBAC5ClD,OAAA;YAAekC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBAC7CnC,OAAA;cAAMkC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,GACxCc,GAAG,CAACL,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEO,CAAC,IAAIA,CAAC,CAACN,WAAW,CAAC,CAAC,CAAC,EAAC,GACjE;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPvC,OAAA;cAAMkC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEe;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC;UAAA,GAJhDU,GAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKR,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDnC,OAAA;YAAKkC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDnC,OAAA;cAAAmC,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpBvC,OAAA;cAAMkC,SAAS,EAAE,8CACf1B,MAAM,CAAC4C,MAAM,KAAK,OAAO,GACrB,+BAA+B,GAC/B,6BAA6B,EAChC;cAAAjB,QAAA,EACA3B,MAAM,CAAC4C,MAAM,CAACP,WAAW,CAAC;YAAC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNvC,OAAA;YAAKkC,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DnC,OAAA;cAAAmC,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBvC,OAAA;cAAAmC,QAAA,EAAO,IAAIkB,IAAI,CAAC7C,MAAM,CAAC8C,UAAU,CAAC,CAACC,cAAc,CAAC;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnC,OAAA;UAAIkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9E/B,MAAM,CAACgD,aAAa,gBACnBxD,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BnC,OAAA;YACEyD,GAAG,EAAEjD,MAAM,CAACgD,aAAc;YAC1BE,GAAG,EAAC,mBAAmB;YACvBxB,SAAS,EAAC;UAAkD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENvC,OAAA;UAAKkC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7CnC,OAAA;YAAAmC,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnC,OAAA;QAAIkC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEpE/B,MAAM,CAAC4C,MAAM,KAAK,OAAO,gBACxBpD,OAAA;QAAKkC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BnC,OAAA;UAAGkC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA2D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjGvC,OAAA;UACEwC,OAAO,EAAEd,cAAe;UACxBiC,QAAQ,EAAE/C,UAAW;UACrBsB,SAAS,EAAC,uHAAuH;UAAAC,QAAA,EAEhIvB,UAAU,GAAG,eAAe,GAAG;QAAiB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENvC,OAAA;QAAKkC,SAAS,EAAC,+BAA+B;QAAAC,QAAA,GAC3C3B,MAAM,CAACoD,iBAAiB,iBACvB5D,OAAA;UACE6D,IAAI,EAAErD,MAAM,CAACoD,iBAAkB;UAC/BE,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,mHAAmH;UAAAC,QAAA,EAC9H;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ,EACA/B,MAAM,CAACwD,iBAAiB,iBACvBhE,OAAA;UACE6D,IAAI,EAAErD,MAAM,CAACwD,iBAAkB;UAC/BF,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,+GAA+G;UAAAC,QAAA,EAC1H;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL/B,MAAM,CAAC4C,MAAM,KAAK,WAAW,IAAI5C,MAAM,CAACoD,iBAAiB,iBACxD5D,OAAA;MAAKkC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnC,OAAA;QAAIkC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9EvC,OAAA;QAAKkC,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDnC,OAAA;UACEyD,GAAG,EAAEjD,MAAM,CAACoD,iBAAkB;UAC9B1B,SAAS,EAAC,uBAAuB;UACjC+B,KAAK,EAAC;QAAgB;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrC,EAAA,CA7NID,UAAoB;EAAA,QACTL,SAAS,EACPC,WAAW;AAAA;AAAAqE,EAAA,GAFxBjE,UAAoB;AA+N1B,eAAeA,UAAU;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}