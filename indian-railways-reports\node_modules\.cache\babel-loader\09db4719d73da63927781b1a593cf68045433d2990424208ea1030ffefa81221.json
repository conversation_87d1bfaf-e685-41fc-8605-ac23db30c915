{"ast": null, "code": "/*!\n * Signature Pad v2.3.2\n * https://github.com/szimek/signature_pad\n *\n * Copyright 2017 <PERSON><PERSON><PERSON>\n * Released under the MIT license\n *\n * The main idea and some parts of the code (e.g. drawing variable width B<PERSON><PERSON> curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://benknowscode.wordpress.com/2012/09/14/path-interpolation-using-cubic-bezier-and-control-point-estimation-in-javascript\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n *\n */\n\nfunction Point(x, y, time) {\n  this.x = x;\n  this.y = y;\n  this.time = time || new Date().getTime();\n}\nPoint.prototype.velocityFrom = function (start) {\n  return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 1;\n};\nPoint.prototype.distanceTo = function (start) {\n  return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n};\nPoint.prototype.equals = function (other) {\n  return this.x === other.x && this.y === other.y && this.time === other.time;\n};\nfunction Bezier(startPoint, control1, control2, endPoint) {\n  this.startPoint = startPoint;\n  this.control1 = control1;\n  this.control2 = control2;\n  this.endPoint = endPoint;\n}\n\n// Returns approximated length.\nBezier.prototype.length = function () {\n  var steps = 10;\n  var length = 0;\n  var px = void 0;\n  var py = void 0;\n  for (var i = 0; i <= steps; i += 1) {\n    var t = i / steps;\n    var cx = this._point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n    var cy = this._point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n    if (i > 0) {\n      var xdiff = cx - px;\n      var ydiff = cy - py;\n      length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n    }\n    px = cx;\n    py = cy;\n  }\n  return length;\n};\n\n/* eslint-disable no-multi-spaces, space-in-parens */\nBezier.prototype._point = function (t, start, c1, c2, end) {\n  return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;\n};\n\n/* eslint-disable */\n\n// http://stackoverflow.com/a/27078401/815507\nfunction throttle(func, wait, options) {\n  var context, args, result;\n  var timeout = null;\n  var previous = 0;\n  if (!options) options = {};\n  var later = function later() {\n    previous = options.leading === false ? 0 : Date.now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n  return function () {\n    var now = Date.now();\n    if (!previous && options.leading === false) previous = now;\n    var remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\nfunction SignaturePad(canvas, options) {\n  var self = this;\n  var opts = options || {};\n  this.velocityFilterWeight = opts.velocityFilterWeight || 0.7;\n  this.minWidth = opts.minWidth || 0.5;\n  this.maxWidth = opts.maxWidth || 2.5;\n  this.throttle = 'throttle' in opts ? opts.throttle : 16; // in miliseconds\n  this.minDistance = 'minDistance' in opts ? opts.minDistance : 5;\n  if (this.throttle) {\n    this._strokeMoveUpdate = throttle(SignaturePad.prototype._strokeUpdate, this.throttle);\n  } else {\n    this._strokeMoveUpdate = SignaturePad.prototype._strokeUpdate;\n  }\n  this.dotSize = opts.dotSize || function () {\n    return (this.minWidth + this.maxWidth) / 2;\n  };\n  this.penColor = opts.penColor || 'black';\n  this.backgroundColor = opts.backgroundColor || 'rgba(0,0,0,0)';\n  this.onBegin = opts.onBegin;\n  this.onEnd = opts.onEnd;\n  this._canvas = canvas;\n  this._ctx = canvas.getContext('2d');\n  this.clear();\n\n  // We need add these inline so they are available to unbind while still having\n  // access to 'self' we could use _.bind but it's not worth adding a dependency.\n  this._handleMouseDown = function (event) {\n    if (event.which === 1) {\n      self._mouseButtonDown = true;\n      self._strokeBegin(event);\n    }\n  };\n  this._handleMouseMove = function (event) {\n    if (self._mouseButtonDown) {\n      self._strokeMoveUpdate(event);\n    }\n  };\n  this._handleMouseUp = function (event) {\n    if (event.which === 1 && self._mouseButtonDown) {\n      self._mouseButtonDown = false;\n      self._strokeEnd(event);\n    }\n  };\n  this._handleTouchStart = function (event) {\n    if (event.targetTouches.length === 1) {\n      var touch = event.changedTouches[0];\n      self._strokeBegin(touch);\n    }\n  };\n  this._handleTouchMove = function (event) {\n    // Prevent scrolling.\n    event.preventDefault();\n    var touch = event.targetTouches[0];\n    self._strokeMoveUpdate(touch);\n  };\n  this._handleTouchEnd = function (event) {\n    var wasCanvasTouched = event.target === self._canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n      self._strokeEnd(event);\n    }\n  };\n\n  // Enable mouse and touch event handlers\n  this.on();\n}\n\n// Public methods\nSignaturePad.prototype.clear = function () {\n  var ctx = this._ctx;\n  var canvas = this._canvas;\n  ctx.fillStyle = this.backgroundColor;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n  this._data = [];\n  this._reset();\n  this._isEmpty = true;\n};\nSignaturePad.prototype.fromDataURL = function (dataUrl) {\n  var _this = this;\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var image = new Image();\n  var ratio = options.ratio || window.devicePixelRatio || 1;\n  var width = options.width || this._canvas.width / ratio;\n  var height = options.height || this._canvas.height / ratio;\n  this._reset();\n  image.src = dataUrl;\n  image.onload = function () {\n    _this._ctx.drawImage(image, 0, 0, width, height);\n  };\n  this._isEmpty = false;\n};\nSignaturePad.prototype.toDataURL = function (type) {\n  var _canvas;\n  switch (type) {\n    case 'image/svg+xml':\n      return this._toSVG();\n    default:\n      for (var _len = arguments.length, options = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        options[_key - 1] = arguments[_key];\n      }\n      return (_canvas = this._canvas).toDataURL.apply(_canvas, [type].concat(options));\n  }\n};\nSignaturePad.prototype.on = function () {\n  this._handleMouseEvents();\n  this._handleTouchEvents();\n};\nSignaturePad.prototype.off = function () {\n  this._canvas.removeEventListener('mousedown', this._handleMouseDown);\n  this._canvas.removeEventListener('mousemove', this._handleMouseMove);\n  document.removeEventListener('mouseup', this._handleMouseUp);\n  this._canvas.removeEventListener('touchstart', this._handleTouchStart);\n  this._canvas.removeEventListener('touchmove', this._handleTouchMove);\n  this._canvas.removeEventListener('touchend', this._handleTouchEnd);\n};\nSignaturePad.prototype.isEmpty = function () {\n  return this._isEmpty;\n};\n\n// Private methods\nSignaturePad.prototype._strokeBegin = function (event) {\n  this._data.push([]);\n  this._reset();\n  this._strokeUpdate(event);\n  if (typeof this.onBegin === 'function') {\n    this.onBegin(event);\n  }\n};\nSignaturePad.prototype._strokeUpdate = function (event) {\n  var x = event.clientX;\n  var y = event.clientY;\n  var point = this._createPoint(x, y);\n  var lastPointGroup = this._data[this._data.length - 1];\n  var lastPoint = lastPointGroup && lastPointGroup[lastPointGroup.length - 1];\n  var isLastPointTooClose = lastPoint && point.distanceTo(lastPoint) < this.minDistance;\n\n  // Skip this point if it's too close to the previous one\n  if (!(lastPoint && isLastPointTooClose)) {\n    var _addPoint = this._addPoint(point),\n      curve = _addPoint.curve,\n      widths = _addPoint.widths;\n    if (curve && widths) {\n      this._drawCurve(curve, widths.start, widths.end);\n    }\n    this._data[this._data.length - 1].push({\n      x: point.x,\n      y: point.y,\n      time: point.time,\n      color: this.penColor\n    });\n  }\n};\nSignaturePad.prototype._strokeEnd = function (event) {\n  var canDrawCurve = this.points.length > 2;\n  var point = this.points[0]; // Point instance\n\n  if (!canDrawCurve && point) {\n    this._drawDot(point);\n  }\n  if (point) {\n    var lastPointGroup = this._data[this._data.length - 1];\n    var lastPoint = lastPointGroup[lastPointGroup.length - 1]; // plain object\n\n    // When drawing a dot, there's only one point in a group, so without this check\n    // such group would end up with exactly the same 2 points.\n    if (!point.equals(lastPoint)) {\n      lastPointGroup.push({\n        x: point.x,\n        y: point.y,\n        time: point.time,\n        color: this.penColor\n      });\n    }\n  }\n  if (typeof this.onEnd === 'function') {\n    this.onEnd(event);\n  }\n};\nSignaturePad.prototype._handleMouseEvents = function () {\n  this._mouseButtonDown = false;\n  this._canvas.addEventListener('mousedown', this._handleMouseDown);\n  this._canvas.addEventListener('mousemove', this._handleMouseMove);\n  document.addEventListener('mouseup', this._handleMouseUp);\n};\nSignaturePad.prototype._handleTouchEvents = function () {\n  // Pass touch events to canvas element on mobile IE11 and Edge.\n  this._canvas.style.msTouchAction = 'none';\n  this._canvas.style.touchAction = 'none';\n  this._canvas.addEventListener('touchstart', this._handleTouchStart);\n  this._canvas.addEventListener('touchmove', this._handleTouchMove);\n  this._canvas.addEventListener('touchend', this._handleTouchEnd);\n};\nSignaturePad.prototype._reset = function () {\n  this.points = [];\n  this._lastVelocity = 0;\n  this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n  this._ctx.fillStyle = this.penColor;\n};\nSignaturePad.prototype._createPoint = function (x, y, time) {\n  var rect = this._canvas.getBoundingClientRect();\n  return new Point(x - rect.left, y - rect.top, time || new Date().getTime());\n};\nSignaturePad.prototype._addPoint = function (point) {\n  var points = this.points;\n  var tmp = void 0;\n  points.push(point);\n  if (points.length > 2) {\n    // To reduce the initial lag make it work with 3 points\n    // by copying the first point to the beginning.\n    if (points.length === 3) points.unshift(points[0]);\n    tmp = this._calculateCurveControlPoints(points[0], points[1], points[2]);\n    var c2 = tmp.c2;\n    tmp = this._calculateCurveControlPoints(points[1], points[2], points[3]);\n    var c3 = tmp.c1;\n    var curve = new Bezier(points[1], c2, c3, points[2]);\n    var widths = this._calculateCurveWidths(curve);\n\n    // Remove the first element from the list,\n    // so that we always have no more than 4 points in points array.\n    points.shift();\n    return {\n      curve: curve,\n      widths: widths\n    };\n  }\n  return {};\n};\nSignaturePad.prototype._calculateCurveControlPoints = function (s1, s2, s3) {\n  var dx1 = s1.x - s2.x;\n  var dy1 = s1.y - s2.y;\n  var dx2 = s2.x - s3.x;\n  var dy2 = s2.y - s3.y;\n  var m1 = {\n    x: (s1.x + s2.x) / 2.0,\n    y: (s1.y + s2.y) / 2.0\n  };\n  var m2 = {\n    x: (s2.x + s3.x) / 2.0,\n    y: (s2.y + s3.y) / 2.0\n  };\n  var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n  var dxm = m1.x - m2.x;\n  var dym = m1.y - m2.y;\n  var k = l2 / (l1 + l2);\n  var cm = {\n    x: m2.x + dxm * k,\n    y: m2.y + dym * k\n  };\n  var tx = s2.x - cm.x;\n  var ty = s2.y - cm.y;\n  return {\n    c1: new Point(m1.x + tx, m1.y + ty),\n    c2: new Point(m2.x + tx, m2.y + ty)\n  };\n};\nSignaturePad.prototype._calculateCurveWidths = function (curve) {\n  var startPoint = curve.startPoint;\n  var endPoint = curve.endPoint;\n  var widths = {\n    start: null,\n    end: null\n  };\n  var velocity = this.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - this.velocityFilterWeight) * this._lastVelocity;\n  var newWidth = this._strokeWidth(velocity);\n  widths.start = this._lastWidth;\n  widths.end = newWidth;\n  this._lastVelocity = velocity;\n  this._lastWidth = newWidth;\n  return widths;\n};\nSignaturePad.prototype._strokeWidth = function (velocity) {\n  return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n};\nSignaturePad.prototype._drawPoint = function (x, y, size) {\n  var ctx = this._ctx;\n  ctx.moveTo(x, y);\n  ctx.arc(x, y, size, 0, 2 * Math.PI, false);\n  this._isEmpty = false;\n};\nSignaturePad.prototype._drawCurve = function (curve, startWidth, endWidth) {\n  var ctx = this._ctx;\n  var widthDelta = endWidth - startWidth;\n  var drawSteps = Math.floor(curve.length());\n  ctx.beginPath();\n  for (var i = 0; i < drawSteps; i += 1) {\n    // Calculate the Bezier (x, y) coordinate for this step.\n    var t = i / drawSteps;\n    var tt = t * t;\n    var ttt = tt * t;\n    var u = 1 - t;\n    var uu = u * u;\n    var uuu = uu * u;\n    var x = uuu * curve.startPoint.x;\n    x += 3 * uu * t * curve.control1.x;\n    x += 3 * u * tt * curve.control2.x;\n    x += ttt * curve.endPoint.x;\n    var y = uuu * curve.startPoint.y;\n    y += 3 * uu * t * curve.control1.y;\n    y += 3 * u * tt * curve.control2.y;\n    y += ttt * curve.endPoint.y;\n    var width = startWidth + ttt * widthDelta;\n    this._drawPoint(x, y, width);\n  }\n  ctx.closePath();\n  ctx.fill();\n};\nSignaturePad.prototype._drawDot = function (point) {\n  var ctx = this._ctx;\n  var width = typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n  ctx.beginPath();\n  this._drawPoint(point.x, point.y, width);\n  ctx.closePath();\n  ctx.fill();\n};\nSignaturePad.prototype._fromData = function (pointGroups, drawCurve, drawDot) {\n  for (var i = 0; i < pointGroups.length; i += 1) {\n    var group = pointGroups[i];\n    if (group.length > 1) {\n      for (var j = 0; j < group.length; j += 1) {\n        var rawPoint = group[j];\n        var point = new Point(rawPoint.x, rawPoint.y, rawPoint.time);\n        var color = rawPoint.color;\n        if (j === 0) {\n          // First point in a group. Nothing to draw yet.\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n          this._reset();\n          this._addPoint(point);\n        } else if (j !== group.length - 1) {\n          // Middle point in a group.\n          var _addPoint2 = this._addPoint(point),\n            curve = _addPoint2.curve,\n            widths = _addPoint2.widths;\n          if (curve && widths) {\n            drawCurve(curve, widths, color);\n          }\n        } else {\n          // Last point in a group. Do nothing.\n        }\n      }\n    } else {\n      this._reset();\n      var _rawPoint = group[0];\n      drawDot(_rawPoint);\n    }\n  }\n};\nSignaturePad.prototype._toSVG = function () {\n  var _this2 = this;\n  var pointGroups = this._data;\n  var canvas = this._canvas;\n  var ratio = Math.max(window.devicePixelRatio || 1, 1);\n  var minX = 0;\n  var minY = 0;\n  var maxX = canvas.width / ratio;\n  var maxY = canvas.height / ratio;\n  var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n  svg.setAttributeNS(null, 'width', canvas.width);\n  svg.setAttributeNS(null, 'height', canvas.height);\n  this._fromData(pointGroups, function (curve, widths, color) {\n    var path = document.createElement('path');\n\n    // Need to check curve for NaN values, these pop up when drawing\n    // lines on the canvas that are not continuous. E.g. Sharp corners\n    // or stopping mid-stroke and than continuing without lifting mouse.\n    if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {\n      var attr = 'M ' + curve.startPoint.x.toFixed(3) + ',' + curve.startPoint.y.toFixed(3) + ' ' + ('C ' + curve.control1.x.toFixed(3) + ',' + curve.control1.y.toFixed(3) + ' ') + (curve.control2.x.toFixed(3) + ',' + curve.control2.y.toFixed(3) + ' ') + (curve.endPoint.x.toFixed(3) + ',' + curve.endPoint.y.toFixed(3));\n      path.setAttribute('d', attr);\n      path.setAttribute('stroke-width', (widths.end * 2.25).toFixed(3));\n      path.setAttribute('stroke', color);\n      path.setAttribute('fill', 'none');\n      path.setAttribute('stroke-linecap', 'round');\n      svg.appendChild(path);\n    }\n  }, function (rawPoint) {\n    var circle = document.createElement('circle');\n    var dotSize = typeof _this2.dotSize === 'function' ? _this2.dotSize() : _this2.dotSize;\n    circle.setAttribute('r', dotSize);\n    circle.setAttribute('cx', rawPoint.x);\n    circle.setAttribute('cy', rawPoint.y);\n    circle.setAttribute('fill', rawPoint.color);\n    svg.appendChild(circle);\n  });\n  var prefix = 'data:image/svg+xml;base64,';\n  var header = '<svg' + ' xmlns=\"http://www.w3.org/2000/svg\"' + ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' + (' viewBox=\"' + minX + ' ' + minY + ' ' + maxX + ' ' + maxY + '\"') + (' width=\"' + maxX + '\"') + (' height=\"' + maxY + '\"') + '>';\n  var body = svg.innerHTML;\n\n  // IE hack for missing innerHTML property on SVGElement\n  if (body === undefined) {\n    var dummy = document.createElement('dummy');\n    var nodes = svg.childNodes;\n    dummy.innerHTML = '';\n    for (var i = 0; i < nodes.length; i += 1) {\n      dummy.appendChild(nodes[i].cloneNode(true));\n    }\n    body = dummy.innerHTML;\n  }\n  var footer = '</svg>';\n  var data = header + body + footer;\n  return prefix + btoa(data);\n};\nSignaturePad.prototype.fromData = function (pointGroups) {\n  var _this3 = this;\n  this.clear();\n  this._fromData(pointGroups, function (curve, widths) {\n    return _this3._drawCurve(curve, widths.start, widths.end);\n  }, function (rawPoint) {\n    return _this3._drawDot(rawPoint);\n  });\n  this._data = pointGroups;\n};\nSignaturePad.prototype.toData = function () {\n  return this._data;\n};\nexport default SignaturePad;", "map": {"version": 3, "names": ["Point", "x", "y", "time", "Date", "getTime", "prototype", "velocityFrom", "start", "distanceTo", "Math", "sqrt", "pow", "equals", "other", "<PERSON><PERSON>", "startPoint", "control1", "control2", "endPoint", "length", "steps", "px", "py", "i", "t", "cx", "_point", "cy", "xdiff", "ydiff", "c1", "c2", "end", "throttle", "func", "wait", "options", "context", "args", "result", "timeout", "previous", "later", "leading", "now", "apply", "remaining", "arguments", "clearTimeout", "trailing", "setTimeout", "SignaturePad", "canvas", "self", "opts", "velocityFilterWeight", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minDistance", "_strokeMoveUpdate", "_strokeUpdate", "dotSize", "penColor", "backgroundColor", "onBegin", "onEnd", "_canvas", "_ctx", "getContext", "clear", "_handleMouseDown", "event", "which", "_mouseButtonDown", "_strokeBegin", "_handleMouseMove", "_handleMouseUp", "_strokeEnd", "_handleTouchStart", "targetTouches", "touch", "changedTouches", "_handleTouchMove", "preventDefault", "_handleTouchEnd", "wasCanvasTouched", "target", "on", "ctx", "fillStyle", "clearRect", "width", "height", "fillRect", "_data", "_reset", "_isEmpty", "fromDataURL", "dataUrl", "_this", "undefined", "image", "Image", "ratio", "window", "devicePixelRatio", "src", "onload", "drawImage", "toDataURL", "type", "_toSVG", "_len", "Array", "_key", "concat", "_handleMouseEvents", "_handleTouchEvents", "off", "removeEventListener", "document", "isEmpty", "push", "clientX", "clientY", "point", "_createPoint", "lastPointGroup", "lastPoint", "isLastPointTooClose", "_addPoint", "curve", "widths", "_drawCurve", "color", "canDrawCurve", "points", "_drawDot", "addEventListener", "style", "msTouchAction", "touchAction", "_lastVelocity", "_lastWidth", "rect", "getBoundingClientRect", "left", "top", "tmp", "unshift", "_calculateCurveControlPoints", "c3", "_calculateCurveWidths", "shift", "s1", "s2", "s3", "dx1", "dy1", "dx2", "dy2", "m1", "m2", "l1", "l2", "dxm", "dym", "k", "cm", "tx", "ty", "velocity", "newWidth", "_strokeWidth", "max", "_drawPoint", "size", "moveTo", "arc", "PI", "startWidth", "endWidth", "<PERSON><PERSON><PERSON><PERSON>", "drawSteps", "floor", "beginPath", "tt", "ttt", "u", "uu", "uuu", "closePath", "fill", "_fromData", "pointGroups", "drawCurve", "drawDot", "group", "j", "rawPoint", "_addPoint2", "_rawPoint", "_this2", "minX", "minY", "maxX", "maxY", "svg", "createElementNS", "setAttributeNS", "path", "createElement", "isNaN", "attr", "toFixed", "setAttribute", "append<PERSON><PERSON><PERSON>", "circle", "prefix", "header", "body", "innerHTML", "dummy", "nodes", "childNodes", "cloneNode", "footer", "data", "btoa", "fromData", "_this3", "toData"], "sources": ["C:/Doc Maker AI/indian-railways-reports/node_modules/signature_pad/dist/signature_pad.mjs"], "sourcesContent": ["/*!\n * Signature Pad v2.3.2\n * https://github.com/szimek/signature_pad\n *\n * Copyright 2017 <PERSON><PERSON><PERSON>\n * Released under the MIT license\n *\n * The main idea and some parts of the code (e.g. drawing variable width B<PERSON><PERSON> curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://benknowscode.wordpress.com/2012/09/14/path-interpolation-using-cubic-bezier-and-control-point-estimation-in-javascript\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n *\n */\n\nfunction Point(x, y, time) {\n  this.x = x;\n  this.y = y;\n  this.time = time || new Date().getTime();\n}\n\nPoint.prototype.velocityFrom = function (start) {\n  return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 1;\n};\n\nPoint.prototype.distanceTo = function (start) {\n  return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n};\n\nPoint.prototype.equals = function (other) {\n  return this.x === other.x && this.y === other.y && this.time === other.time;\n};\n\nfunction Bezier(startPoint, control1, control2, endPoint) {\n  this.startPoint = startPoint;\n  this.control1 = control1;\n  this.control2 = control2;\n  this.endPoint = endPoint;\n}\n\n// Returns approximated length.\nBezier.prototype.length = function () {\n  var steps = 10;\n  var length = 0;\n  var px = void 0;\n  var py = void 0;\n\n  for (var i = 0; i <= steps; i += 1) {\n    var t = i / steps;\n    var cx = this._point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n    var cy = this._point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n    if (i > 0) {\n      var xdiff = cx - px;\n      var ydiff = cy - py;\n      length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n    }\n    px = cx;\n    py = cy;\n  }\n\n  return length;\n};\n\n/* eslint-disable no-multi-spaces, space-in-parens */\nBezier.prototype._point = function (t, start, c1, c2, end) {\n  return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;\n};\n\n/* eslint-disable */\n\n// http://stackoverflow.com/a/27078401/815507\nfunction throttle(func, wait, options) {\n  var context, args, result;\n  var timeout = null;\n  var previous = 0;\n  if (!options) options = {};\n  var later = function later() {\n    previous = options.leading === false ? 0 : Date.now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n  return function () {\n    var now = Date.now();\n    if (!previous && options.leading === false) previous = now;\n    var remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\n\nfunction SignaturePad(canvas, options) {\n  var self = this;\n  var opts = options || {};\n\n  this.velocityFilterWeight = opts.velocityFilterWeight || 0.7;\n  this.minWidth = opts.minWidth || 0.5;\n  this.maxWidth = opts.maxWidth || 2.5;\n  this.throttle = 'throttle' in opts ? opts.throttle : 16; // in miliseconds\n  this.minDistance = 'minDistance' in opts ? opts.minDistance : 5;\n\n  if (this.throttle) {\n    this._strokeMoveUpdate = throttle(SignaturePad.prototype._strokeUpdate, this.throttle);\n  } else {\n    this._strokeMoveUpdate = SignaturePad.prototype._strokeUpdate;\n  }\n\n  this.dotSize = opts.dotSize || function () {\n    return (this.minWidth + this.maxWidth) / 2;\n  };\n  this.penColor = opts.penColor || 'black';\n  this.backgroundColor = opts.backgroundColor || 'rgba(0,0,0,0)';\n  this.onBegin = opts.onBegin;\n  this.onEnd = opts.onEnd;\n\n  this._canvas = canvas;\n  this._ctx = canvas.getContext('2d');\n  this.clear();\n\n  // We need add these inline so they are available to unbind while still having\n  // access to 'self' we could use _.bind but it's not worth adding a dependency.\n  this._handleMouseDown = function (event) {\n    if (event.which === 1) {\n      self._mouseButtonDown = true;\n      self._strokeBegin(event);\n    }\n  };\n\n  this._handleMouseMove = function (event) {\n    if (self._mouseButtonDown) {\n      self._strokeMoveUpdate(event);\n    }\n  };\n\n  this._handleMouseUp = function (event) {\n    if (event.which === 1 && self._mouseButtonDown) {\n      self._mouseButtonDown = false;\n      self._strokeEnd(event);\n    }\n  };\n\n  this._handleTouchStart = function (event) {\n    if (event.targetTouches.length === 1) {\n      var touch = event.changedTouches[0];\n      self._strokeBegin(touch);\n    }\n  };\n\n  this._handleTouchMove = function (event) {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    var touch = event.targetTouches[0];\n    self._strokeMoveUpdate(touch);\n  };\n\n  this._handleTouchEnd = function (event) {\n    var wasCanvasTouched = event.target === self._canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n      self._strokeEnd(event);\n    }\n  };\n\n  // Enable mouse and touch event handlers\n  this.on();\n}\n\n// Public methods\nSignaturePad.prototype.clear = function () {\n  var ctx = this._ctx;\n  var canvas = this._canvas;\n\n  ctx.fillStyle = this.backgroundColor;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n  this._data = [];\n  this._reset();\n  this._isEmpty = true;\n};\n\nSignaturePad.prototype.fromDataURL = function (dataUrl) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var image = new Image();\n  var ratio = options.ratio || window.devicePixelRatio || 1;\n  var width = options.width || this._canvas.width / ratio;\n  var height = options.height || this._canvas.height / ratio;\n\n  this._reset();\n  image.src = dataUrl;\n  image.onload = function () {\n    _this._ctx.drawImage(image, 0, 0, width, height);\n  };\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype.toDataURL = function (type) {\n  var _canvas;\n\n  switch (type) {\n    case 'image/svg+xml':\n      return this._toSVG();\n    default:\n      for (var _len = arguments.length, options = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        options[_key - 1] = arguments[_key];\n      }\n\n      return (_canvas = this._canvas).toDataURL.apply(_canvas, [type].concat(options));\n  }\n};\n\nSignaturePad.prototype.on = function () {\n  this._handleMouseEvents();\n  this._handleTouchEvents();\n};\n\nSignaturePad.prototype.off = function () {\n  this._canvas.removeEventListener('mousedown', this._handleMouseDown);\n  this._canvas.removeEventListener('mousemove', this._handleMouseMove);\n  document.removeEventListener('mouseup', this._handleMouseUp);\n\n  this._canvas.removeEventListener('touchstart', this._handleTouchStart);\n  this._canvas.removeEventListener('touchmove', this._handleTouchMove);\n  this._canvas.removeEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype.isEmpty = function () {\n  return this._isEmpty;\n};\n\n// Private methods\nSignaturePad.prototype._strokeBegin = function (event) {\n  this._data.push([]);\n  this._reset();\n  this._strokeUpdate(event);\n\n  if (typeof this.onBegin === 'function') {\n    this.onBegin(event);\n  }\n};\n\nSignaturePad.prototype._strokeUpdate = function (event) {\n  var x = event.clientX;\n  var y = event.clientY;\n\n  var point = this._createPoint(x, y);\n  var lastPointGroup = this._data[this._data.length - 1];\n  var lastPoint = lastPointGroup && lastPointGroup[lastPointGroup.length - 1];\n  var isLastPointTooClose = lastPoint && point.distanceTo(lastPoint) < this.minDistance;\n\n  // Skip this point if it's too close to the previous one\n  if (!(lastPoint && isLastPointTooClose)) {\n    var _addPoint = this._addPoint(point),\n        curve = _addPoint.curve,\n        widths = _addPoint.widths;\n\n    if (curve && widths) {\n      this._drawCurve(curve, widths.start, widths.end);\n    }\n\n    this._data[this._data.length - 1].push({\n      x: point.x,\n      y: point.y,\n      time: point.time,\n      color: this.penColor\n    });\n  }\n};\n\nSignaturePad.prototype._strokeEnd = function (event) {\n  var canDrawCurve = this.points.length > 2;\n  var point = this.points[0]; // Point instance\n\n  if (!canDrawCurve && point) {\n    this._drawDot(point);\n  }\n\n  if (point) {\n    var lastPointGroup = this._data[this._data.length - 1];\n    var lastPoint = lastPointGroup[lastPointGroup.length - 1]; // plain object\n\n    // When drawing a dot, there's only one point in a group, so without this check\n    // such group would end up with exactly the same 2 points.\n    if (!point.equals(lastPoint)) {\n      lastPointGroup.push({\n        x: point.x,\n        y: point.y,\n        time: point.time,\n        color: this.penColor\n      });\n    }\n  }\n\n  if (typeof this.onEnd === 'function') {\n    this.onEnd(event);\n  }\n};\n\nSignaturePad.prototype._handleMouseEvents = function () {\n  this._mouseButtonDown = false;\n\n  this._canvas.addEventListener('mousedown', this._handleMouseDown);\n  this._canvas.addEventListener('mousemove', this._handleMouseMove);\n  document.addEventListener('mouseup', this._handleMouseUp);\n};\n\nSignaturePad.prototype._handleTouchEvents = function () {\n  // Pass touch events to canvas element on mobile IE11 and Edge.\n  this._canvas.style.msTouchAction = 'none';\n  this._canvas.style.touchAction = 'none';\n\n  this._canvas.addEventListener('touchstart', this._handleTouchStart);\n  this._canvas.addEventListener('touchmove', this._handleTouchMove);\n  this._canvas.addEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype._reset = function () {\n  this.points = [];\n  this._lastVelocity = 0;\n  this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n  this._ctx.fillStyle = this.penColor;\n};\n\nSignaturePad.prototype._createPoint = function (x, y, time) {\n  var rect = this._canvas.getBoundingClientRect();\n\n  return new Point(x - rect.left, y - rect.top, time || new Date().getTime());\n};\n\nSignaturePad.prototype._addPoint = function (point) {\n  var points = this.points;\n  var tmp = void 0;\n\n  points.push(point);\n\n  if (points.length > 2) {\n    // To reduce the initial lag make it work with 3 points\n    // by copying the first point to the beginning.\n    if (points.length === 3) points.unshift(points[0]);\n\n    tmp = this._calculateCurveControlPoints(points[0], points[1], points[2]);\n    var c2 = tmp.c2;\n    tmp = this._calculateCurveControlPoints(points[1], points[2], points[3]);\n    var c3 = tmp.c1;\n    var curve = new Bezier(points[1], c2, c3, points[2]);\n    var widths = this._calculateCurveWidths(curve);\n\n    // Remove the first element from the list,\n    // so that we always have no more than 4 points in points array.\n    points.shift();\n\n    return { curve: curve, widths: widths };\n  }\n\n  return {};\n};\n\nSignaturePad.prototype._calculateCurveControlPoints = function (s1, s2, s3) {\n  var dx1 = s1.x - s2.x;\n  var dy1 = s1.y - s2.y;\n  var dx2 = s2.x - s3.x;\n  var dy2 = s2.y - s3.y;\n\n  var m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n  var m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n  var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n  var dxm = m1.x - m2.x;\n  var dym = m1.y - m2.y;\n\n  var k = l2 / (l1 + l2);\n  var cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n  var tx = s2.x - cm.x;\n  var ty = s2.y - cm.y;\n\n  return {\n    c1: new Point(m1.x + tx, m1.y + ty),\n    c2: new Point(m2.x + tx, m2.y + ty)\n  };\n};\n\nSignaturePad.prototype._calculateCurveWidths = function (curve) {\n  var startPoint = curve.startPoint;\n  var endPoint = curve.endPoint;\n  var widths = { start: null, end: null };\n\n  var velocity = this.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - this.velocityFilterWeight) * this._lastVelocity;\n\n  var newWidth = this._strokeWidth(velocity);\n\n  widths.start = this._lastWidth;\n  widths.end = newWidth;\n\n  this._lastVelocity = velocity;\n  this._lastWidth = newWidth;\n\n  return widths;\n};\n\nSignaturePad.prototype._strokeWidth = function (velocity) {\n  return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n};\n\nSignaturePad.prototype._drawPoint = function (x, y, size) {\n  var ctx = this._ctx;\n\n  ctx.moveTo(x, y);\n  ctx.arc(x, y, size, 0, 2 * Math.PI, false);\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype._drawCurve = function (curve, startWidth, endWidth) {\n  var ctx = this._ctx;\n  var widthDelta = endWidth - startWidth;\n  var drawSteps = Math.floor(curve.length());\n\n  ctx.beginPath();\n\n  for (var i = 0; i < drawSteps; i += 1) {\n    // Calculate the Bezier (x, y) coordinate for this step.\n    var t = i / drawSteps;\n    var tt = t * t;\n    var ttt = tt * t;\n    var u = 1 - t;\n    var uu = u * u;\n    var uuu = uu * u;\n\n    var x = uuu * curve.startPoint.x;\n    x += 3 * uu * t * curve.control1.x;\n    x += 3 * u * tt * curve.control2.x;\n    x += ttt * curve.endPoint.x;\n\n    var y = uuu * curve.startPoint.y;\n    y += 3 * uu * t * curve.control1.y;\n    y += 3 * u * tt * curve.control2.y;\n    y += ttt * curve.endPoint.y;\n\n    var width = startWidth + ttt * widthDelta;\n    this._drawPoint(x, y, width);\n  }\n\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._drawDot = function (point) {\n  var ctx = this._ctx;\n  var width = typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n\n  ctx.beginPath();\n  this._drawPoint(point.x, point.y, width);\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._fromData = function (pointGroups, drawCurve, drawDot) {\n  for (var i = 0; i < pointGroups.length; i += 1) {\n    var group = pointGroups[i];\n\n    if (group.length > 1) {\n      for (var j = 0; j < group.length; j += 1) {\n        var rawPoint = group[j];\n        var point = new Point(rawPoint.x, rawPoint.y, rawPoint.time);\n        var color = rawPoint.color;\n\n        if (j === 0) {\n          // First point in a group. Nothing to draw yet.\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n          this._reset();\n\n          this._addPoint(point);\n        } else if (j !== group.length - 1) {\n          // Middle point in a group.\n          var _addPoint2 = this._addPoint(point),\n              curve = _addPoint2.curve,\n              widths = _addPoint2.widths;\n\n          if (curve && widths) {\n            drawCurve(curve, widths, color);\n          }\n        } else {\n          // Last point in a group. Do nothing.\n        }\n      }\n    } else {\n      this._reset();\n      var _rawPoint = group[0];\n      drawDot(_rawPoint);\n    }\n  }\n};\n\nSignaturePad.prototype._toSVG = function () {\n  var _this2 = this;\n\n  var pointGroups = this._data;\n  var canvas = this._canvas;\n  var ratio = Math.max(window.devicePixelRatio || 1, 1);\n  var minX = 0;\n  var minY = 0;\n  var maxX = canvas.width / ratio;\n  var maxY = canvas.height / ratio;\n  var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n  svg.setAttributeNS(null, 'width', canvas.width);\n  svg.setAttributeNS(null, 'height', canvas.height);\n\n  this._fromData(pointGroups, function (curve, widths, color) {\n    var path = document.createElement('path');\n\n    // Need to check curve for NaN values, these pop up when drawing\n    // lines on the canvas that are not continuous. E.g. Sharp corners\n    // or stopping mid-stroke and than continuing without lifting mouse.\n    if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {\n      var attr = 'M ' + curve.startPoint.x.toFixed(3) + ',' + curve.startPoint.y.toFixed(3) + ' ' + ('C ' + curve.control1.x.toFixed(3) + ',' + curve.control1.y.toFixed(3) + ' ') + (curve.control2.x.toFixed(3) + ',' + curve.control2.y.toFixed(3) + ' ') + (curve.endPoint.x.toFixed(3) + ',' + curve.endPoint.y.toFixed(3));\n\n      path.setAttribute('d', attr);\n      path.setAttribute('stroke-width', (widths.end * 2.25).toFixed(3));\n      path.setAttribute('stroke', color);\n      path.setAttribute('fill', 'none');\n      path.setAttribute('stroke-linecap', 'round');\n\n      svg.appendChild(path);\n    }\n  }, function (rawPoint) {\n    var circle = document.createElement('circle');\n    var dotSize = typeof _this2.dotSize === 'function' ? _this2.dotSize() : _this2.dotSize;\n    circle.setAttribute('r', dotSize);\n    circle.setAttribute('cx', rawPoint.x);\n    circle.setAttribute('cy', rawPoint.y);\n    circle.setAttribute('fill', rawPoint.color);\n\n    svg.appendChild(circle);\n  });\n\n  var prefix = 'data:image/svg+xml;base64,';\n  var header = '<svg' + ' xmlns=\"http://www.w3.org/2000/svg\"' + ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' + (' viewBox=\"' + minX + ' ' + minY + ' ' + maxX + ' ' + maxY + '\"') + (' width=\"' + maxX + '\"') + (' height=\"' + maxY + '\"') + '>';\n  var body = svg.innerHTML;\n\n  // IE hack for missing innerHTML property on SVGElement\n  if (body === undefined) {\n    var dummy = document.createElement('dummy');\n    var nodes = svg.childNodes;\n    dummy.innerHTML = '';\n\n    for (var i = 0; i < nodes.length; i += 1) {\n      dummy.appendChild(nodes[i].cloneNode(true));\n    }\n\n    body = dummy.innerHTML;\n  }\n\n  var footer = '</svg>';\n  var data = header + body + footer;\n\n  return prefix + btoa(data);\n};\n\nSignaturePad.prototype.fromData = function (pointGroups) {\n  var _this3 = this;\n\n  this.clear();\n\n  this._fromData(pointGroups, function (curve, widths) {\n    return _this3._drawCurve(curve, widths.start, widths.end);\n  }, function (rawPoint) {\n    return _this3._drawDot(rawPoint);\n  });\n\n  this._data = pointGroups;\n};\n\nSignaturePad.prototype.toData = function () {\n  return this._data;\n};\n\nexport default SignaturePad;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAE;EACzB,IAAI,CAACF,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,IAAI,GAAGA,IAAI,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;AAC1C;AAEAL,KAAK,CAACM,SAAS,CAACC,YAAY,GAAG,UAAUC,KAAK,EAAE;EAC9C,OAAO,IAAI,CAACL,IAAI,KAAKK,KAAK,CAACL,IAAI,GAAG,IAAI,CAACM,UAAU,CAACD,KAAK,CAAC,IAAI,IAAI,CAACL,IAAI,GAAGK,KAAK,CAACL,IAAI,CAAC,GAAG,CAAC;AACzF,CAAC;AAEDH,KAAK,CAACM,SAAS,CAACG,UAAU,GAAG,UAAUD,KAAK,EAAE;EAC5C,OAAOE,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,IAAI,CAACX,CAAC,GAAGO,KAAK,CAACP,CAAC,EAAE,CAAC,CAAC,GAAGS,IAAI,CAACE,GAAG,CAAC,IAAI,CAACV,CAAC,GAAGM,KAAK,CAACN,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,CAAC;AAEDF,KAAK,CAACM,SAAS,CAACO,MAAM,GAAG,UAAUC,KAAK,EAAE;EACxC,OAAO,IAAI,CAACb,CAAC,KAAKa,KAAK,CAACb,CAAC,IAAI,IAAI,CAACC,CAAC,KAAKY,KAAK,CAACZ,CAAC,IAAI,IAAI,CAACC,IAAI,KAAKW,KAAK,CAACX,IAAI;AAC7E,CAAC;AAED,SAASY,MAAMA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACxD,IAAI,CAACH,UAAU,GAAGA,UAAU;EAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;AAC1B;;AAEA;AACAJ,MAAM,CAACT,SAAS,CAACc,MAAM,GAAG,YAAY;EACpC,IAAIC,KAAK,GAAG,EAAE;EACd,IAAID,MAAM,GAAG,CAAC;EACd,IAAIE,EAAE,GAAG,KAAK,CAAC;EACf,IAAIC,EAAE,GAAG,KAAK,CAAC;EAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,KAAK,EAAEG,CAAC,IAAI,CAAC,EAAE;IAClC,IAAIC,CAAC,GAAGD,CAAC,GAAGH,KAAK;IACjB,IAAIK,EAAE,GAAG,IAAI,CAACC,MAAM,CAACF,CAAC,EAAE,IAAI,CAACT,UAAU,CAACf,CAAC,EAAE,IAAI,CAACgB,QAAQ,CAAChB,CAAC,EAAE,IAAI,CAACiB,QAAQ,CAACjB,CAAC,EAAE,IAAI,CAACkB,QAAQ,CAAClB,CAAC,CAAC;IAC7F,IAAI2B,EAAE,GAAG,IAAI,CAACD,MAAM,CAACF,CAAC,EAAE,IAAI,CAACT,UAAU,CAACd,CAAC,EAAE,IAAI,CAACe,QAAQ,CAACf,CAAC,EAAE,IAAI,CAACgB,QAAQ,CAAChB,CAAC,EAAE,IAAI,CAACiB,QAAQ,CAACjB,CAAC,CAAC;IAC7F,IAAIsB,CAAC,GAAG,CAAC,EAAE;MACT,IAAIK,KAAK,GAAGH,EAAE,GAAGJ,EAAE;MACnB,IAAIQ,KAAK,GAAGF,EAAE,GAAGL,EAAE;MACnBH,MAAM,IAAIV,IAAI,CAACC,IAAI,CAACkB,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,CAAC;IACpD;IACAR,EAAE,GAAGI,EAAE;IACPH,EAAE,GAAGK,EAAE;EACT;EAEA,OAAOR,MAAM;AACf,CAAC;;AAED;AACAL,MAAM,CAACT,SAAS,CAACqB,MAAM,GAAG,UAAUF,CAAC,EAAEjB,KAAK,EAAEuB,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAE;EACzD,OAAOzB,KAAK,IAAI,GAAG,GAAGiB,CAAC,CAAC,IAAI,GAAG,GAAGA,CAAC,CAAC,IAAI,GAAG,GAAGA,CAAC,CAAC,GAAG,GAAG,GAAGM,EAAE,IAAI,GAAG,GAAGN,CAAC,CAAC,IAAI,GAAG,GAAGA,CAAC,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAGO,EAAE,IAAI,GAAG,GAAGP,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGQ,GAAG,GAAGR,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAC1I,CAAC;;AAED;;AAEA;AACA,SAASS,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACrC,IAAIC,OAAO,EAAEC,IAAI,EAAEC,MAAM;EACzB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAI,CAACL,OAAO,EAAEA,OAAO,GAAG,CAAC,CAAC;EAC1B,IAAIM,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3BD,QAAQ,GAAGL,OAAO,CAACO,OAAO,KAAK,KAAK,GAAG,CAAC,GAAGxC,IAAI,CAACyC,GAAG,CAAC,CAAC;IACrDJ,OAAO,GAAG,IAAI;IACdD,MAAM,GAAGL,IAAI,CAACW,KAAK,CAACR,OAAO,EAAEC,IAAI,CAAC;IAClC,IAAI,CAACE,OAAO,EAAEH,OAAO,GAAGC,IAAI,GAAG,IAAI;EACrC,CAAC;EACD,OAAO,YAAY;IACjB,IAAIM,GAAG,GAAGzC,IAAI,CAACyC,GAAG,CAAC,CAAC;IACpB,IAAI,CAACH,QAAQ,IAAIL,OAAO,CAACO,OAAO,KAAK,KAAK,EAAEF,QAAQ,GAAGG,GAAG;IAC1D,IAAIE,SAAS,GAAGX,IAAI,IAAIS,GAAG,GAAGH,QAAQ,CAAC;IACvCJ,OAAO,GAAG,IAAI;IACdC,IAAI,GAAGS,SAAS;IAChB,IAAID,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAGX,IAAI,EAAE;MACtC,IAAIK,OAAO,EAAE;QACXQ,YAAY,CAACR,OAAO,CAAC;QACrBA,OAAO,GAAG,IAAI;MAChB;MACAC,QAAQ,GAAGG,GAAG;MACdL,MAAM,GAAGL,IAAI,CAACW,KAAK,CAACR,OAAO,EAAEC,IAAI,CAAC;MAClC,IAAI,CAACE,OAAO,EAAEH,OAAO,GAAGC,IAAI,GAAG,IAAI;IACrC,CAAC,MAAM,IAAI,CAACE,OAAO,IAAIJ,OAAO,CAACa,QAAQ,KAAK,KAAK,EAAE;MACjDT,OAAO,GAAGU,UAAU,CAACR,KAAK,EAAEI,SAAS,CAAC;IACxC;IACA,OAAOP,MAAM;EACf,CAAC;AACH;AAEA,SAASY,YAAYA,CAACC,MAAM,EAAEhB,OAAO,EAAE;EACrC,IAAIiB,IAAI,GAAG,IAAI;EACf,IAAIC,IAAI,GAAGlB,OAAO,IAAI,CAAC,CAAC;EAExB,IAAI,CAACmB,oBAAoB,GAAGD,IAAI,CAACC,oBAAoB,IAAI,GAAG;EAC5D,IAAI,CAACC,QAAQ,GAAGF,IAAI,CAACE,QAAQ,IAAI,GAAG;EACpC,IAAI,CAACC,QAAQ,GAAGH,IAAI,CAACG,QAAQ,IAAI,GAAG;EACpC,IAAI,CAACxB,QAAQ,GAAG,UAAU,IAAIqB,IAAI,GAAGA,IAAI,CAACrB,QAAQ,GAAG,EAAE,CAAC,CAAC;EACzD,IAAI,CAACyB,WAAW,GAAG,aAAa,IAAIJ,IAAI,GAAGA,IAAI,CAACI,WAAW,GAAG,CAAC;EAE/D,IAAI,IAAI,CAACzB,QAAQ,EAAE;IACjB,IAAI,CAAC0B,iBAAiB,GAAG1B,QAAQ,CAACkB,YAAY,CAAC9C,SAAS,CAACuD,aAAa,EAAE,IAAI,CAAC3B,QAAQ,CAAC;EACxF,CAAC,MAAM;IACL,IAAI,CAAC0B,iBAAiB,GAAGR,YAAY,CAAC9C,SAAS,CAACuD,aAAa;EAC/D;EAEA,IAAI,CAACC,OAAO,GAAGP,IAAI,CAACO,OAAO,IAAI,YAAY;IACzC,OAAO,CAAC,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACC,QAAQ,IAAI,CAAC;EAC5C,CAAC;EACD,IAAI,CAACK,QAAQ,GAAGR,IAAI,CAACQ,QAAQ,IAAI,OAAO;EACxC,IAAI,CAACC,eAAe,GAAGT,IAAI,CAACS,eAAe,IAAI,eAAe;EAC9D,IAAI,CAACC,OAAO,GAAGV,IAAI,CAACU,OAAO;EAC3B,IAAI,CAACC,KAAK,GAAGX,IAAI,CAACW,KAAK;EAEvB,IAAI,CAACC,OAAO,GAAGd,MAAM;EACrB,IAAI,CAACe,IAAI,GAAGf,MAAM,CAACgB,UAAU,CAAC,IAAI,CAAC;EACnC,IAAI,CAACC,KAAK,CAAC,CAAC;;EAEZ;EACA;EACA,IAAI,CAACC,gBAAgB,GAAG,UAAUC,KAAK,EAAE;IACvC,IAAIA,KAAK,CAACC,KAAK,KAAK,CAAC,EAAE;MACrBnB,IAAI,CAACoB,gBAAgB,GAAG,IAAI;MAC5BpB,IAAI,CAACqB,YAAY,CAACH,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,IAAI,CAACI,gBAAgB,GAAG,UAAUJ,KAAK,EAAE;IACvC,IAAIlB,IAAI,CAACoB,gBAAgB,EAAE;MACzBpB,IAAI,CAACM,iBAAiB,CAACY,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,IAAI,CAACK,cAAc,GAAG,UAAUL,KAAK,EAAE;IACrC,IAAIA,KAAK,CAACC,KAAK,KAAK,CAAC,IAAInB,IAAI,CAACoB,gBAAgB,EAAE;MAC9CpB,IAAI,CAACoB,gBAAgB,GAAG,KAAK;MAC7BpB,IAAI,CAACwB,UAAU,CAACN,KAAK,CAAC;IACxB;EACF,CAAC;EAED,IAAI,CAACO,iBAAiB,GAAG,UAAUP,KAAK,EAAE;IACxC,IAAIA,KAAK,CAACQ,aAAa,CAAC5D,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI6D,KAAK,GAAGT,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC;MACnC5B,IAAI,CAACqB,YAAY,CAACM,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,IAAI,CAACE,gBAAgB,GAAG,UAAUX,KAAK,EAAE;IACvC;IACAA,KAAK,CAACY,cAAc,CAAC,CAAC;IAEtB,IAAIH,KAAK,GAAGT,KAAK,CAACQ,aAAa,CAAC,CAAC,CAAC;IAClC1B,IAAI,CAACM,iBAAiB,CAACqB,KAAK,CAAC;EAC/B,CAAC;EAED,IAAI,CAACI,eAAe,GAAG,UAAUb,KAAK,EAAE;IACtC,IAAIc,gBAAgB,GAAGd,KAAK,CAACe,MAAM,KAAKjC,IAAI,CAACa,OAAO;IACpD,IAAImB,gBAAgB,EAAE;MACpBd,KAAK,CAACY,cAAc,CAAC,CAAC;MACtB9B,IAAI,CAACwB,UAAU,CAACN,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,IAAI,CAACgB,EAAE,CAAC,CAAC;AACX;;AAEA;AACApC,YAAY,CAAC9C,SAAS,CAACgE,KAAK,GAAG,YAAY;EACzC,IAAImB,GAAG,GAAG,IAAI,CAACrB,IAAI;EACnB,IAAIf,MAAM,GAAG,IAAI,CAACc,OAAO;EAEzBsB,GAAG,CAACC,SAAS,GAAG,IAAI,CAAC1B,eAAe;EACpCyB,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEtC,MAAM,CAACuC,KAAK,EAAEvC,MAAM,CAACwC,MAAM,CAAC;EAChDJ,GAAG,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEzC,MAAM,CAACuC,KAAK,EAAEvC,MAAM,CAACwC,MAAM,CAAC;EAE/C,IAAI,CAACE,KAAK,GAAG,EAAE;EACf,IAAI,CAACC,MAAM,CAAC,CAAC;EACb,IAAI,CAACC,QAAQ,GAAG,IAAI;AACtB,CAAC;AAED7C,YAAY,CAAC9C,SAAS,CAAC4F,WAAW,GAAG,UAAUC,OAAO,EAAE;EACtD,IAAIC,KAAK,GAAG,IAAI;EAEhB,IAAI/D,OAAO,GAAGW,SAAS,CAAC5B,MAAM,GAAG,CAAC,IAAI4B,SAAS,CAAC,CAAC,CAAC,KAAKqD,SAAS,GAAGrD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpF,IAAIsD,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;EACvB,IAAIC,KAAK,GAAGnE,OAAO,CAACmE,KAAK,IAAIC,MAAM,CAACC,gBAAgB,IAAI,CAAC;EACzD,IAAId,KAAK,GAAGvD,OAAO,CAACuD,KAAK,IAAI,IAAI,CAACzB,OAAO,CAACyB,KAAK,GAAGY,KAAK;EACvD,IAAIX,MAAM,GAAGxD,OAAO,CAACwD,MAAM,IAAI,IAAI,CAAC1B,OAAO,CAAC0B,MAAM,GAAGW,KAAK;EAE1D,IAAI,CAACR,MAAM,CAAC,CAAC;EACbM,KAAK,CAACK,GAAG,GAAGR,OAAO;EACnBG,KAAK,CAACM,MAAM,GAAG,YAAY;IACzBR,KAAK,CAAChC,IAAI,CAACyC,SAAS,CAACP,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEV,KAAK,EAAEC,MAAM,CAAC;EAClD,CAAC;EACD,IAAI,CAACI,QAAQ,GAAG,KAAK;AACvB,CAAC;AAED7C,YAAY,CAAC9C,SAAS,CAACwG,SAAS,GAAG,UAAUC,IAAI,EAAE;EACjD,IAAI5C,OAAO;EAEX,QAAQ4C,IAAI;IACV,KAAK,eAAe;MAClB,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC;IACtB;MACE,KAAK,IAAIC,IAAI,GAAGjE,SAAS,CAAC5B,MAAM,EAAEiB,OAAO,GAAG6E,KAAK,CAACD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;QACzG9E,OAAO,CAAC8E,IAAI,GAAG,CAAC,CAAC,GAAGnE,SAAS,CAACmE,IAAI,CAAC;MACrC;MAEA,OAAO,CAAChD,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE2C,SAAS,CAAChE,KAAK,CAACqB,OAAO,EAAE,CAAC4C,IAAI,CAAC,CAACK,MAAM,CAAC/E,OAAO,CAAC,CAAC;EACpF;AACF,CAAC;AAEDe,YAAY,CAAC9C,SAAS,CAACkF,EAAE,GAAG,YAAY;EACtC,IAAI,CAAC6B,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;AAC3B,CAAC;AAEDlE,YAAY,CAAC9C,SAAS,CAACiH,GAAG,GAAG,YAAY;EACvC,IAAI,CAACpD,OAAO,CAACqD,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACjD,gBAAgB,CAAC;EACpE,IAAI,CAACJ,OAAO,CAACqD,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC5C,gBAAgB,CAAC;EACpE6C,QAAQ,CAACD,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC3C,cAAc,CAAC;EAE5D,IAAI,CAACV,OAAO,CAACqD,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACzC,iBAAiB,CAAC;EACtE,IAAI,CAACZ,OAAO,CAACqD,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACrC,gBAAgB,CAAC;EACpE,IAAI,CAAChB,OAAO,CAACqD,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACnC,eAAe,CAAC;AACpE,CAAC;AAEDjC,YAAY,CAAC9C,SAAS,CAACoH,OAAO,GAAG,YAAY;EAC3C,OAAO,IAAI,CAACzB,QAAQ;AACtB,CAAC;;AAED;AACA7C,YAAY,CAAC9C,SAAS,CAACqE,YAAY,GAAG,UAAUH,KAAK,EAAE;EACrD,IAAI,CAACuB,KAAK,CAAC4B,IAAI,CAAC,EAAE,CAAC;EACnB,IAAI,CAAC3B,MAAM,CAAC,CAAC;EACb,IAAI,CAACnC,aAAa,CAACW,KAAK,CAAC;EAEzB,IAAI,OAAO,IAAI,CAACP,OAAO,KAAK,UAAU,EAAE;IACtC,IAAI,CAACA,OAAO,CAACO,KAAK,CAAC;EACrB;AACF,CAAC;AAEDpB,YAAY,CAAC9C,SAAS,CAACuD,aAAa,GAAG,UAAUW,KAAK,EAAE;EACtD,IAAIvE,CAAC,GAAGuE,KAAK,CAACoD,OAAO;EACrB,IAAI1H,CAAC,GAAGsE,KAAK,CAACqD,OAAO;EAErB,IAAIC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC9H,CAAC,EAAEC,CAAC,CAAC;EACnC,IAAI8H,cAAc,GAAG,IAAI,CAACjC,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC3E,MAAM,GAAG,CAAC,CAAC;EACtD,IAAI6G,SAAS,GAAGD,cAAc,IAAIA,cAAc,CAACA,cAAc,CAAC5G,MAAM,GAAG,CAAC,CAAC;EAC3E,IAAI8G,mBAAmB,GAAGD,SAAS,IAAIH,KAAK,CAACrH,UAAU,CAACwH,SAAS,CAAC,GAAG,IAAI,CAACtE,WAAW;;EAErF;EACA,IAAI,EAAEsE,SAAS,IAAIC,mBAAmB,CAAC,EAAE;IACvC,IAAIC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACL,KAAK,CAAC;MACjCM,KAAK,GAAGD,SAAS,CAACC,KAAK;MACvBC,MAAM,GAAGF,SAAS,CAACE,MAAM;IAE7B,IAAID,KAAK,IAAIC,MAAM,EAAE;MACnB,IAAI,CAACC,UAAU,CAACF,KAAK,EAAEC,MAAM,CAAC7H,KAAK,EAAE6H,MAAM,CAACpG,GAAG,CAAC;IAClD;IAEA,IAAI,CAAC8D,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC3E,MAAM,GAAG,CAAC,CAAC,CAACuG,IAAI,CAAC;MACrC1H,CAAC,EAAE6H,KAAK,CAAC7H,CAAC;MACVC,CAAC,EAAE4H,KAAK,CAAC5H,CAAC;MACVC,IAAI,EAAE2H,KAAK,CAAC3H,IAAI;MAChBoI,KAAK,EAAE,IAAI,CAACxE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEDX,YAAY,CAAC9C,SAAS,CAACwE,UAAU,GAAG,UAAUN,KAAK,EAAE;EACnD,IAAIgE,YAAY,GAAG,IAAI,CAACC,MAAM,CAACrH,MAAM,GAAG,CAAC;EACzC,IAAI0G,KAAK,GAAG,IAAI,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B,IAAI,CAACD,YAAY,IAAIV,KAAK,EAAE;IAC1B,IAAI,CAACY,QAAQ,CAACZ,KAAK,CAAC;EACtB;EAEA,IAAIA,KAAK,EAAE;IACT,IAAIE,cAAc,GAAG,IAAI,CAACjC,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC3E,MAAM,GAAG,CAAC,CAAC;IACtD,IAAI6G,SAAS,GAAGD,cAAc,CAACA,cAAc,CAAC5G,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE3D;IACA;IACA,IAAI,CAAC0G,KAAK,CAACjH,MAAM,CAACoH,SAAS,CAAC,EAAE;MAC5BD,cAAc,CAACL,IAAI,CAAC;QAClB1H,CAAC,EAAE6H,KAAK,CAAC7H,CAAC;QACVC,CAAC,EAAE4H,KAAK,CAAC5H,CAAC;QACVC,IAAI,EAAE2H,KAAK,CAAC3H,IAAI;QAChBoI,KAAK,EAAE,IAAI,CAACxE;MACd,CAAC,CAAC;IACJ;EACF;EAEA,IAAI,OAAO,IAAI,CAACG,KAAK,KAAK,UAAU,EAAE;IACpC,IAAI,CAACA,KAAK,CAACM,KAAK,CAAC;EACnB;AACF,CAAC;AAEDpB,YAAY,CAAC9C,SAAS,CAAC+G,kBAAkB,GAAG,YAAY;EACtD,IAAI,CAAC3C,gBAAgB,GAAG,KAAK;EAE7B,IAAI,CAACP,OAAO,CAACwE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACpE,gBAAgB,CAAC;EACjE,IAAI,CAACJ,OAAO,CAACwE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC/D,gBAAgB,CAAC;EACjE6C,QAAQ,CAACkB,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC9D,cAAc,CAAC;AAC3D,CAAC;AAEDzB,YAAY,CAAC9C,SAAS,CAACgH,kBAAkB,GAAG,YAAY;EACtD;EACA,IAAI,CAACnD,OAAO,CAACyE,KAAK,CAACC,aAAa,GAAG,MAAM;EACzC,IAAI,CAAC1E,OAAO,CAACyE,KAAK,CAACE,WAAW,GAAG,MAAM;EAEvC,IAAI,CAAC3E,OAAO,CAACwE,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC5D,iBAAiB,CAAC;EACnE,IAAI,CAACZ,OAAO,CAACwE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACxD,gBAAgB,CAAC;EACjE,IAAI,CAAChB,OAAO,CAACwE,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACtD,eAAe,CAAC;AACjE,CAAC;AAEDjC,YAAY,CAAC9C,SAAS,CAAC0F,MAAM,GAAG,YAAY;EAC1C,IAAI,CAACyC,MAAM,GAAG,EAAE;EAChB,IAAI,CAACM,aAAa,GAAG,CAAC;EACtB,IAAI,CAACC,UAAU,GAAG,CAAC,IAAI,CAACvF,QAAQ,GAAG,IAAI,CAACC,QAAQ,IAAI,CAAC;EACrD,IAAI,CAACU,IAAI,CAACsB,SAAS,GAAG,IAAI,CAAC3B,QAAQ;AACrC,CAAC;AAEDX,YAAY,CAAC9C,SAAS,CAACyH,YAAY,GAAG,UAAU9H,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAE;EAC1D,IAAI8I,IAAI,GAAG,IAAI,CAAC9E,OAAO,CAAC+E,qBAAqB,CAAC,CAAC;EAE/C,OAAO,IAAIlJ,KAAK,CAACC,CAAC,GAAGgJ,IAAI,CAACE,IAAI,EAAEjJ,CAAC,GAAG+I,IAAI,CAACG,GAAG,EAAEjJ,IAAI,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED+C,YAAY,CAAC9C,SAAS,CAAC6H,SAAS,GAAG,UAAUL,KAAK,EAAE;EAClD,IAAIW,MAAM,GAAG,IAAI,CAACA,MAAM;EACxB,IAAIY,GAAG,GAAG,KAAK,CAAC;EAEhBZ,MAAM,CAACd,IAAI,CAACG,KAAK,CAAC;EAElB,IAAIW,MAAM,CAACrH,MAAM,GAAG,CAAC,EAAE;IACrB;IACA;IACA,IAAIqH,MAAM,CAACrH,MAAM,KAAK,CAAC,EAAEqH,MAAM,CAACa,OAAO,CAACb,MAAM,CAAC,CAAC,CAAC,CAAC;IAElDY,GAAG,GAAG,IAAI,CAACE,4BAA4B,CAACd,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE,IAAIzG,EAAE,GAAGqH,GAAG,CAACrH,EAAE;IACfqH,GAAG,GAAG,IAAI,CAACE,4BAA4B,CAACd,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE,IAAIe,EAAE,GAAGH,GAAG,CAACtH,EAAE;IACf,IAAIqG,KAAK,GAAG,IAAIrH,MAAM,CAAC0H,MAAM,CAAC,CAAC,CAAC,EAAEzG,EAAE,EAAEwH,EAAE,EAAEf,MAAM,CAAC,CAAC,CAAC,CAAC;IACpD,IAAIJ,MAAM,GAAG,IAAI,CAACoB,qBAAqB,CAACrB,KAAK,CAAC;;IAE9C;IACA;IACAK,MAAM,CAACiB,KAAK,CAAC,CAAC;IAEd,OAAO;MAAEtB,KAAK,EAAEA,KAAK;MAAEC,MAAM,EAAEA;IAAO,CAAC;EACzC;EAEA,OAAO,CAAC,CAAC;AACX,CAAC;AAEDjF,YAAY,CAAC9C,SAAS,CAACiJ,4BAA4B,GAAG,UAAUI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC1E,IAAIC,GAAG,GAAGH,EAAE,CAAC1J,CAAC,GAAG2J,EAAE,CAAC3J,CAAC;EACrB,IAAI8J,GAAG,GAAGJ,EAAE,CAACzJ,CAAC,GAAG0J,EAAE,CAAC1J,CAAC;EACrB,IAAI8J,GAAG,GAAGJ,EAAE,CAAC3J,CAAC,GAAG4J,EAAE,CAAC5J,CAAC;EACrB,IAAIgK,GAAG,GAAGL,EAAE,CAAC1J,CAAC,GAAG2J,EAAE,CAAC3J,CAAC;EAErB,IAAIgK,EAAE,GAAG;IAAEjK,CAAC,EAAE,CAAC0J,EAAE,CAAC1J,CAAC,GAAG2J,EAAE,CAAC3J,CAAC,IAAI,GAAG;IAAEC,CAAC,EAAE,CAACyJ,EAAE,CAACzJ,CAAC,GAAG0J,EAAE,CAAC1J,CAAC,IAAI;EAAI,CAAC;EAC3D,IAAIiK,EAAE,GAAG;IAAElK,CAAC,EAAE,CAAC2J,EAAE,CAAC3J,CAAC,GAAG4J,EAAE,CAAC5J,CAAC,IAAI,GAAG;IAAEC,CAAC,EAAE,CAAC0J,EAAE,CAAC1J,CAAC,GAAG2J,EAAE,CAAC3J,CAAC,IAAI;EAAI,CAAC;EAE3D,IAAIkK,EAAE,GAAG1J,IAAI,CAACC,IAAI,CAACmJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC;EACzC,IAAIM,EAAE,GAAG3J,IAAI,CAACC,IAAI,CAACqJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC;EAEzC,IAAIK,GAAG,GAAGJ,EAAE,CAACjK,CAAC,GAAGkK,EAAE,CAAClK,CAAC;EACrB,IAAIsK,GAAG,GAAGL,EAAE,CAAChK,CAAC,GAAGiK,EAAE,CAACjK,CAAC;EAErB,IAAIsK,CAAC,GAAGH,EAAE,IAAID,EAAE,GAAGC,EAAE,CAAC;EACtB,IAAII,EAAE,GAAG;IAAExK,CAAC,EAAEkK,EAAE,CAAClK,CAAC,GAAGqK,GAAG,GAAGE,CAAC;IAAEtK,CAAC,EAAEiK,EAAE,CAACjK,CAAC,GAAGqK,GAAG,GAAGC;EAAE,CAAC;EAEjD,IAAIE,EAAE,GAAGd,EAAE,CAAC3J,CAAC,GAAGwK,EAAE,CAACxK,CAAC;EACpB,IAAI0K,EAAE,GAAGf,EAAE,CAAC1J,CAAC,GAAGuK,EAAE,CAACvK,CAAC;EAEpB,OAAO;IACL6B,EAAE,EAAE,IAAI/B,KAAK,CAACkK,EAAE,CAACjK,CAAC,GAAGyK,EAAE,EAAER,EAAE,CAAChK,CAAC,GAAGyK,EAAE,CAAC;IACnC3I,EAAE,EAAE,IAAIhC,KAAK,CAACmK,EAAE,CAAClK,CAAC,GAAGyK,EAAE,EAAEP,EAAE,CAACjK,CAAC,GAAGyK,EAAE;EACpC,CAAC;AACH,CAAC;AAEDvH,YAAY,CAAC9C,SAAS,CAACmJ,qBAAqB,GAAG,UAAUrB,KAAK,EAAE;EAC9D,IAAIpH,UAAU,GAAGoH,KAAK,CAACpH,UAAU;EACjC,IAAIG,QAAQ,GAAGiH,KAAK,CAACjH,QAAQ;EAC7B,IAAIkH,MAAM,GAAG;IAAE7H,KAAK,EAAE,IAAI;IAAEyB,GAAG,EAAE;EAAK,CAAC;EAEvC,IAAI2I,QAAQ,GAAG,IAAI,CAACpH,oBAAoB,GAAGrC,QAAQ,CAACZ,YAAY,CAACS,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACwC,oBAAoB,IAAI,IAAI,CAACuF,aAAa;EAEnI,IAAI8B,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;EAE1CvC,MAAM,CAAC7H,KAAK,GAAG,IAAI,CAACwI,UAAU;EAC9BX,MAAM,CAACpG,GAAG,GAAG4I,QAAQ;EAErB,IAAI,CAAC9B,aAAa,GAAG6B,QAAQ;EAC7B,IAAI,CAAC5B,UAAU,GAAG6B,QAAQ;EAE1B,OAAOxC,MAAM;AACf,CAAC;AAEDjF,YAAY,CAAC9C,SAAS,CAACwK,YAAY,GAAG,UAAUF,QAAQ,EAAE;EACxD,OAAOlK,IAAI,CAACqK,GAAG,CAAC,IAAI,CAACrH,QAAQ,IAAIkH,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnH,QAAQ,CAAC;AAChE,CAAC;AAEDL,YAAY,CAAC9C,SAAS,CAAC0K,UAAU,GAAG,UAAU/K,CAAC,EAAEC,CAAC,EAAE+K,IAAI,EAAE;EACxD,IAAIxF,GAAG,GAAG,IAAI,CAACrB,IAAI;EAEnBqB,GAAG,CAACyF,MAAM,CAACjL,CAAC,EAAEC,CAAC,CAAC;EAChBuF,GAAG,CAAC0F,GAAG,CAAClL,CAAC,EAAEC,CAAC,EAAE+K,IAAI,EAAE,CAAC,EAAE,CAAC,GAAGvK,IAAI,CAAC0K,EAAE,EAAE,KAAK,CAAC;EAC1C,IAAI,CAACnF,QAAQ,GAAG,KAAK;AACvB,CAAC;AAED7C,YAAY,CAAC9C,SAAS,CAACgI,UAAU,GAAG,UAAUF,KAAK,EAAEiD,UAAU,EAAEC,QAAQ,EAAE;EACzE,IAAI7F,GAAG,GAAG,IAAI,CAACrB,IAAI;EACnB,IAAImH,UAAU,GAAGD,QAAQ,GAAGD,UAAU;EACtC,IAAIG,SAAS,GAAG9K,IAAI,CAAC+K,KAAK,CAACrD,KAAK,CAAChH,MAAM,CAAC,CAAC,CAAC;EAE1CqE,GAAG,CAACiG,SAAS,CAAC,CAAC;EAEf,KAAK,IAAIlK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgK,SAAS,EAAEhK,CAAC,IAAI,CAAC,EAAE;IACrC;IACA,IAAIC,CAAC,GAAGD,CAAC,GAAGgK,SAAS;IACrB,IAAIG,EAAE,GAAGlK,CAAC,GAAGA,CAAC;IACd,IAAImK,GAAG,GAAGD,EAAE,GAAGlK,CAAC;IAChB,IAAIoK,CAAC,GAAG,CAAC,GAAGpK,CAAC;IACb,IAAIqK,EAAE,GAAGD,CAAC,GAAGA,CAAC;IACd,IAAIE,GAAG,GAAGD,EAAE,GAAGD,CAAC;IAEhB,IAAI5L,CAAC,GAAG8L,GAAG,GAAG3D,KAAK,CAACpH,UAAU,CAACf,CAAC;IAChCA,CAAC,IAAI,CAAC,GAAG6L,EAAE,GAAGrK,CAAC,GAAG2G,KAAK,CAACnH,QAAQ,CAAChB,CAAC;IAClCA,CAAC,IAAI,CAAC,GAAG4L,CAAC,GAAGF,EAAE,GAAGvD,KAAK,CAAClH,QAAQ,CAACjB,CAAC;IAClCA,CAAC,IAAI2L,GAAG,GAAGxD,KAAK,CAACjH,QAAQ,CAAClB,CAAC;IAE3B,IAAIC,CAAC,GAAG6L,GAAG,GAAG3D,KAAK,CAACpH,UAAU,CAACd,CAAC;IAChCA,CAAC,IAAI,CAAC,GAAG4L,EAAE,GAAGrK,CAAC,GAAG2G,KAAK,CAACnH,QAAQ,CAACf,CAAC;IAClCA,CAAC,IAAI,CAAC,GAAG2L,CAAC,GAAGF,EAAE,GAAGvD,KAAK,CAAClH,QAAQ,CAAChB,CAAC;IAClCA,CAAC,IAAI0L,GAAG,GAAGxD,KAAK,CAACjH,QAAQ,CAACjB,CAAC;IAE3B,IAAI0F,KAAK,GAAGyF,UAAU,GAAGO,GAAG,GAAGL,UAAU;IACzC,IAAI,CAACP,UAAU,CAAC/K,CAAC,EAAEC,CAAC,EAAE0F,KAAK,CAAC;EAC9B;EAEAH,GAAG,CAACuG,SAAS,CAAC,CAAC;EACfvG,GAAG,CAACwG,IAAI,CAAC,CAAC;AACZ,CAAC;AAED7I,YAAY,CAAC9C,SAAS,CAACoI,QAAQ,GAAG,UAAUZ,KAAK,EAAE;EACjD,IAAIrC,GAAG,GAAG,IAAI,CAACrB,IAAI;EACnB,IAAIwB,KAAK,GAAG,OAAO,IAAI,CAAC9B,OAAO,KAAK,UAAU,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC,GAAG,IAAI,CAACA,OAAO;EAE9E2B,GAAG,CAACiG,SAAS,CAAC,CAAC;EACf,IAAI,CAACV,UAAU,CAAClD,KAAK,CAAC7H,CAAC,EAAE6H,KAAK,CAAC5H,CAAC,EAAE0F,KAAK,CAAC;EACxCH,GAAG,CAACuG,SAAS,CAAC,CAAC;EACfvG,GAAG,CAACwG,IAAI,CAAC,CAAC;AACZ,CAAC;AAED7I,YAAY,CAAC9C,SAAS,CAAC4L,SAAS,GAAG,UAAUC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAC5E,KAAK,IAAI7K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2K,WAAW,CAAC/K,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;IAC9C,IAAI8K,KAAK,GAAGH,WAAW,CAAC3K,CAAC,CAAC;IAE1B,IAAI8K,KAAK,CAAClL,MAAM,GAAG,CAAC,EAAE;MACpB,KAAK,IAAImL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAClL,MAAM,EAAEmL,CAAC,IAAI,CAAC,EAAE;QACxC,IAAIC,QAAQ,GAAGF,KAAK,CAACC,CAAC,CAAC;QACvB,IAAIzE,KAAK,GAAG,IAAI9H,KAAK,CAACwM,QAAQ,CAACvM,CAAC,EAAEuM,QAAQ,CAACtM,CAAC,EAAEsM,QAAQ,CAACrM,IAAI,CAAC;QAC5D,IAAIoI,KAAK,GAAGiE,QAAQ,CAACjE,KAAK;QAE1B,IAAIgE,CAAC,KAAK,CAAC,EAAE;UACX;;UAEA;UACA;UACA,IAAI,CAACxI,QAAQ,GAAGwE,KAAK;UACrB,IAAI,CAACvC,MAAM,CAAC,CAAC;UAEb,IAAI,CAACmC,SAAS,CAACL,KAAK,CAAC;QACvB,CAAC,MAAM,IAAIyE,CAAC,KAAKD,KAAK,CAAClL,MAAM,GAAG,CAAC,EAAE;UACjC;UACA,IAAIqL,UAAU,GAAG,IAAI,CAACtE,SAAS,CAACL,KAAK,CAAC;YAClCM,KAAK,GAAGqE,UAAU,CAACrE,KAAK;YACxBC,MAAM,GAAGoE,UAAU,CAACpE,MAAM;UAE9B,IAAID,KAAK,IAAIC,MAAM,EAAE;YACnB+D,SAAS,CAAChE,KAAK,EAAEC,MAAM,EAAEE,KAAK,CAAC;UACjC;QACF,CAAC,MAAM;UACL;QAAA;MAEJ;IACF,CAAC,MAAM;MACL,IAAI,CAACvC,MAAM,CAAC,CAAC;MACb,IAAI0G,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACxBD,OAAO,CAACK,SAAS,CAAC;IACpB;EACF;AACF,CAAC;AAEDtJ,YAAY,CAAC9C,SAAS,CAAC0G,MAAM,GAAG,YAAY;EAC1C,IAAI2F,MAAM,GAAG,IAAI;EAEjB,IAAIR,WAAW,GAAG,IAAI,CAACpG,KAAK;EAC5B,IAAI1C,MAAM,GAAG,IAAI,CAACc,OAAO;EACzB,IAAIqC,KAAK,GAAG9F,IAAI,CAACqK,GAAG,CAACtE,MAAM,CAACC,gBAAgB,IAAI,CAAC,EAAE,CAAC,CAAC;EACrD,IAAIkG,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAGzJ,MAAM,CAACuC,KAAK,GAAGY,KAAK;EAC/B,IAAIuG,IAAI,GAAG1J,MAAM,CAACwC,MAAM,GAAGW,KAAK;EAChC,IAAIwG,GAAG,GAAGvF,QAAQ,CAACwF,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;EAEvED,GAAG,CAACE,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE7J,MAAM,CAACuC,KAAK,CAAC;EAC/CoH,GAAG,CAACE,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE7J,MAAM,CAACwC,MAAM,CAAC;EAEjD,IAAI,CAACqG,SAAS,CAACC,WAAW,EAAE,UAAU/D,KAAK,EAAEC,MAAM,EAAEE,KAAK,EAAE;IAC1D,IAAI4E,IAAI,GAAG1F,QAAQ,CAAC2F,aAAa,CAAC,MAAM,CAAC;;IAEzC;IACA;IACA;IACA,IAAI,CAACC,KAAK,CAACjF,KAAK,CAACnH,QAAQ,CAAChB,CAAC,CAAC,IAAI,CAACoN,KAAK,CAACjF,KAAK,CAACnH,QAAQ,CAACf,CAAC,CAAC,IAAI,CAACmN,KAAK,CAACjF,KAAK,CAAClH,QAAQ,CAACjB,CAAC,CAAC,IAAI,CAACoN,KAAK,CAACjF,KAAK,CAAClH,QAAQ,CAAChB,CAAC,CAAC,EAAE;MAChH,IAAIoN,IAAI,GAAG,IAAI,GAAGlF,KAAK,CAACpH,UAAU,CAACf,CAAC,CAACsN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGnF,KAAK,CAACpH,UAAU,CAACd,CAAC,CAACqN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,GAAGnF,KAAK,CAACnH,QAAQ,CAAChB,CAAC,CAACsN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGnF,KAAK,CAACnH,QAAQ,CAACf,CAAC,CAACqN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAInF,KAAK,CAAClH,QAAQ,CAACjB,CAAC,CAACsN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGnF,KAAK,CAAClH,QAAQ,CAAChB,CAAC,CAACqN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAInF,KAAK,CAACjH,QAAQ,CAAClB,CAAC,CAACsN,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGnF,KAAK,CAACjH,QAAQ,CAACjB,CAAC,CAACqN,OAAO,CAAC,CAAC,CAAC,CAAC;MAE1TJ,IAAI,CAACK,YAAY,CAAC,GAAG,EAAEF,IAAI,CAAC;MAC5BH,IAAI,CAACK,YAAY,CAAC,cAAc,EAAE,CAACnF,MAAM,CAACpG,GAAG,GAAG,IAAI,EAAEsL,OAAO,CAAC,CAAC,CAAC,CAAC;MACjEJ,IAAI,CAACK,YAAY,CAAC,QAAQ,EAAEjF,KAAK,CAAC;MAClC4E,IAAI,CAACK,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;MACjCL,IAAI,CAACK,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC;MAE5CR,GAAG,CAACS,WAAW,CAACN,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,UAAUX,QAAQ,EAAE;IACrB,IAAIkB,MAAM,GAAGjG,QAAQ,CAAC2F,aAAa,CAAC,QAAQ,CAAC;IAC7C,IAAItJ,OAAO,GAAG,OAAO6I,MAAM,CAAC7I,OAAO,KAAK,UAAU,GAAG6I,MAAM,CAAC7I,OAAO,CAAC,CAAC,GAAG6I,MAAM,CAAC7I,OAAO;IACtF4J,MAAM,CAACF,YAAY,CAAC,GAAG,EAAE1J,OAAO,CAAC;IACjC4J,MAAM,CAACF,YAAY,CAAC,IAAI,EAAEhB,QAAQ,CAACvM,CAAC,CAAC;IACrCyN,MAAM,CAACF,YAAY,CAAC,IAAI,EAAEhB,QAAQ,CAACtM,CAAC,CAAC;IACrCwN,MAAM,CAACF,YAAY,CAAC,MAAM,EAAEhB,QAAQ,CAACjE,KAAK,CAAC;IAE3CyE,GAAG,CAACS,WAAW,CAACC,MAAM,CAAC;EACzB,CAAC,CAAC;EAEF,IAAIC,MAAM,GAAG,4BAA4B;EACzC,IAAIC,MAAM,GAAG,MAAM,GAAG,qCAAqC,GAAG,6CAA6C,IAAI,YAAY,GAAGhB,IAAI,GAAG,GAAG,GAAGC,IAAI,GAAG,GAAG,GAAGC,IAAI,GAAG,GAAG,GAAGC,IAAI,GAAG,GAAG,CAAC,IAAI,UAAU,GAAGD,IAAI,GAAG,GAAG,CAAC,IAAI,WAAW,GAAGC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;EAC/O,IAAIc,IAAI,GAAGb,GAAG,CAACc,SAAS;;EAExB;EACA,IAAID,IAAI,KAAKxH,SAAS,EAAE;IACtB,IAAI0H,KAAK,GAAGtG,QAAQ,CAAC2F,aAAa,CAAC,OAAO,CAAC;IAC3C,IAAIY,KAAK,GAAGhB,GAAG,CAACiB,UAAU;IAC1BF,KAAK,CAACD,SAAS,GAAG,EAAE;IAEpB,KAAK,IAAItM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwM,KAAK,CAAC5M,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;MACxCuM,KAAK,CAACN,WAAW,CAACO,KAAK,CAACxM,CAAC,CAAC,CAAC0M,SAAS,CAAC,IAAI,CAAC,CAAC;IAC7C;IAEAL,IAAI,GAAGE,KAAK,CAACD,SAAS;EACxB;EAEA,IAAIK,MAAM,GAAG,QAAQ;EACrB,IAAIC,IAAI,GAAGR,MAAM,GAAGC,IAAI,GAAGM,MAAM;EAEjC,OAAOR,MAAM,GAAGU,IAAI,CAACD,IAAI,CAAC;AAC5B,CAAC;AAEDhL,YAAY,CAAC9C,SAAS,CAACgO,QAAQ,GAAG,UAAUnC,WAAW,EAAE;EACvD,IAAIoC,MAAM,GAAG,IAAI;EAEjB,IAAI,CAACjK,KAAK,CAAC,CAAC;EAEZ,IAAI,CAAC4H,SAAS,CAACC,WAAW,EAAE,UAAU/D,KAAK,EAAEC,MAAM,EAAE;IACnD,OAAOkG,MAAM,CAACjG,UAAU,CAACF,KAAK,EAAEC,MAAM,CAAC7H,KAAK,EAAE6H,MAAM,CAACpG,GAAG,CAAC;EAC3D,CAAC,EAAE,UAAUuK,QAAQ,EAAE;IACrB,OAAO+B,MAAM,CAAC7F,QAAQ,CAAC8D,QAAQ,CAAC;EAClC,CAAC,CAAC;EAEF,IAAI,CAACzG,KAAK,GAAGoG,WAAW;AAC1B,CAAC;AAED/I,YAAY,CAAC9C,SAAS,CAACkO,MAAM,GAAG,YAAY;EAC1C,OAAO,IAAI,CAACzI,KAAK;AACnB,CAAC;AAED,eAAe3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}