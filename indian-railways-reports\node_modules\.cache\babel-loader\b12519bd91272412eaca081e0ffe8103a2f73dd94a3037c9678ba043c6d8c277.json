{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = require(\"./version\");\nexports.DEFAULT_HEADERS = {\n  'X-Client-Info': `postgrest-js/${version_1.version}`\n};", "map": {"version": 3, "names": ["version_1", "require", "exports", "DEFAULT_HEADERS", "version"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\postgrest-js\\src\\constants.ts"], "sourcesContent": ["import { version } from './version'\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version}` }\n"], "mappings": ";;;;;;AAAA,MAAAA,SAAA,GAAAC,OAAA;AACaC,OAAA,CAAAC,eAAe,GAAG;EAAE,eAAe,EAAE,gBAAgBH,SAAA,CAAAI,OAAO;AAAE,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}