{"ast": null, "code": "import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants';\nimport Push from './lib/push';\nimport Timer from './lib/timer';\nimport RealtimePresence from './RealtimePresence';\nimport * as Transformers from './lib/transformers';\nimport { httpEndpointURL } from './lib/transformers';\nexport var REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nexport var REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n  REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n  REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n  REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n  REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nexport var REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n  REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n  REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n  REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n  REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n  constructor(/** Topic name can be any string. */\n  topic, params = {\n    config: {}\n  }, socket) {\n    this.topic = topic;\n    this.params = params;\n    this.socket = socket;\n    this.bindings = {};\n    this.state = CHANNEL_STATES.closed;\n    this.joinedOnce = false;\n    this.pushBuffer = [];\n    this.subTopic = topic.replace(/^realtime:/i, '');\n    this.params.config = Object.assign({\n      broadcast: {\n        ack: false,\n        self: false\n      },\n      presence: {\n        key: ''\n      },\n      private: false\n    }, params.config);\n    this.timeout = this.socket.timeout;\n    this.joinPush = new Push(this, CHANNEL_EVENTS.join, this.params, this.timeout);\n    this.rejoinTimer = new Timer(() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n    this.joinPush.receive('ok', () => {\n      this.state = CHANNEL_STATES.joined;\n      this.rejoinTimer.reset();\n      this.pushBuffer.forEach(pushEvent => pushEvent.send());\n      this.pushBuffer = [];\n    });\n    this._onClose(() => {\n      this.rejoinTimer.reset();\n      this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n      this.state = CHANNEL_STATES.closed;\n      this.socket._remove(this);\n    });\n    this._onError(reason => {\n      if (this._isLeaving() || this._isClosed()) {\n        return;\n      }\n      this.socket.log('channel', `error ${this.topic}`, reason);\n      this.state = CHANNEL_STATES.errored;\n      this.rejoinTimer.scheduleTimeout();\n    });\n    this.joinPush.receive('timeout', () => {\n      if (!this._isJoining()) {\n        return;\n      }\n      this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n      this.state = CHANNEL_STATES.errored;\n      this.rejoinTimer.scheduleTimeout();\n    });\n    this._on(CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n      this._trigger(this._replyEventName(ref), payload);\n    });\n    this.presence = new RealtimePresence(this);\n    this.broadcastEndpointURL = httpEndpointURL(this.socket.endPoint) + '/api/broadcast';\n    this.private = this.params.config.private || false;\n  }\n  /** Subscribe registers your client with the server */\n  subscribe(callback, timeout = this.timeout) {\n    var _a, _b;\n    if (!this.socket.isConnected()) {\n      this.socket.connect();\n    }\n    if (this.state == CHANNEL_STATES.closed) {\n      const {\n        config: {\n          broadcast,\n          presence,\n          private: isPrivate\n        }\n      } = this.params;\n      this._onError(e => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n      this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n      const accessTokenPayload = {};\n      const config = {\n        broadcast,\n        presence,\n        postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map(r => r.filter)) !== null && _b !== void 0 ? _b : [],\n        private: isPrivate\n      };\n      if (this.socket.accessTokenValue) {\n        accessTokenPayload.access_token = this.socket.accessTokenValue;\n      }\n      this.updateJoinPayload(Object.assign({\n        config\n      }, accessTokenPayload));\n      this.joinedOnce = true;\n      this._rejoin(timeout);\n      this.joinPush.receive('ok', async ({\n        postgres_changes\n      }) => {\n        var _a;\n        this.socket.setAuth();\n        if (postgres_changes === undefined) {\n          callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n          return;\n        } else {\n          const clientPostgresBindings = this.bindings.postgres_changes;\n          const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n          const newPostgresBindings = [];\n          for (let i = 0; i < bindingsLen; i++) {\n            const clientPostgresBinding = clientPostgresBindings[i];\n            const {\n              filter: {\n                event,\n                schema,\n                table,\n                filter\n              }\n            } = clientPostgresBinding;\n            const serverPostgresFilter = postgres_changes && postgres_changes[i];\n            if (serverPostgresFilter && serverPostgresFilter.event === event && serverPostgresFilter.schema === schema && serverPostgresFilter.table === table && serverPostgresFilter.filter === filter) {\n              newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), {\n                id: serverPostgresFilter.id\n              }));\n            } else {\n              this.unsubscribe();\n              this.state = CHANNEL_STATES.errored;\n              callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n              return;\n            }\n          }\n          this.bindings.postgres_changes = newPostgresBindings;\n          callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n          return;\n        }\n      }).receive('error', error => {\n        this.state = CHANNEL_STATES.errored;\n        callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n        return;\n      }).receive('timeout', () => {\n        callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n        return;\n      });\n    }\n    return this;\n  }\n  presenceState() {\n    return this.presence.state;\n  }\n  async track(payload, opts = {}) {\n    return await this.send({\n      type: 'presence',\n      event: 'track',\n      payload\n    }, opts.timeout || this.timeout);\n  }\n  async untrack(opts = {}) {\n    return await this.send({\n      type: 'presence',\n      event: 'untrack'\n    }, opts);\n  }\n  on(type, filter, callback) {\n    return this._on(type, filter, callback);\n  }\n  /**\n   * Sends a message into the channel.\n   *\n   * @param args Arguments to send to channel\n   * @param args.type The type of event to send\n   * @param args.event The name of the event being sent\n   * @param args.payload Payload to be sent\n   * @param opts Options to be used during the send process\n   */\n  async send(args, opts = {}) {\n    var _a, _b;\n    if (!this._canPush() && args.type === 'broadcast') {\n      const {\n        event,\n        payload: endpoint_payload\n      } = args;\n      const authorization = this.socket.accessTokenValue ? `Bearer ${this.socket.accessTokenValue}` : '';\n      const options = {\n        method: 'POST',\n        headers: {\n          Authorization: authorization,\n          apikey: this.socket.apiKey ? this.socket.apiKey : '',\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages: [{\n            topic: this.subTopic,\n            event,\n            payload: endpoint_payload,\n            private: this.private\n          }]\n        })\n      };\n      try {\n        const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n        await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n        return response.ok ? 'ok' : 'error';\n      } catch (error) {\n        if (error.name === 'AbortError') {\n          return 'timed out';\n        } else {\n          return 'error';\n        }\n      }\n    } else {\n      return new Promise(resolve => {\n        var _a, _b, _c;\n        const push = this._push(args.type, args, opts.timeout || this.timeout);\n        if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n          resolve('ok');\n        }\n        push.receive('ok', () => resolve('ok'));\n        push.receive('error', () => resolve('error'));\n        push.receive('timeout', () => resolve('timed out'));\n      });\n    }\n  }\n  updateJoinPayload(payload) {\n    this.joinPush.updatePayload(payload);\n  }\n  /**\n   * Leaves the channel.\n   *\n   * Unsubscribes from server events, and instructs channel to terminate on server.\n   * Triggers onClose() hooks.\n   *\n   * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n   * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n   */\n  unsubscribe(timeout = this.timeout) {\n    this.state = CHANNEL_STATES.leaving;\n    const onClose = () => {\n      this.socket.log('channel', `leave ${this.topic}`);\n      this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef());\n    };\n    this.joinPush.destroy();\n    let leavePush = null;\n    return new Promise(resolve => {\n      leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout);\n      leavePush.receive('ok', () => {\n        onClose();\n        resolve('ok');\n      }).receive('timeout', () => {\n        onClose();\n        resolve('timed out');\n      }).receive('error', () => {\n        resolve('error');\n      });\n      leavePush.send();\n      if (!this._canPush()) {\n        leavePush.trigger('ok', {});\n      }\n    }).finally(() => {\n      leavePush === null || leavePush === void 0 ? void 0 : leavePush.destroy();\n    });\n  }\n  /**\n   * Teardown the channel.\n   *\n   * Destroys and stops related timers.\n   */\n  teardown() {\n    this.pushBuffer.forEach(push => push.destroy());\n    this.rejoinTimer && clearTimeout(this.rejoinTimer.timer);\n    this.joinPush.destroy();\n  }\n  /** @internal */\n  async _fetchWithTimeout(url, options, timeout) {\n    const controller = new AbortController();\n    const id = setTimeout(() => controller.abort(), timeout);\n    const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), {\n      signal: controller.signal\n    }));\n    clearTimeout(id);\n    return response;\n  }\n  /** @internal */\n  _push(event, payload, timeout = this.timeout) {\n    if (!this.joinedOnce) {\n      throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n    }\n    let pushEvent = new Push(this, event, payload, timeout);\n    if (this._canPush()) {\n      pushEvent.send();\n    } else {\n      pushEvent.startTimeout();\n      this.pushBuffer.push(pushEvent);\n    }\n    return pushEvent;\n  }\n  /**\n   * Overridable message hook\n   *\n   * Receives all events for specialized message handling before dispatching to the channel callbacks.\n   * Must return the payload, modified or unmodified.\n   *\n   * @internal\n   */\n  _onMessage(_event, payload, _ref) {\n    return payload;\n  }\n  /** @internal */\n  _isMember(topic) {\n    return this.topic === topic;\n  }\n  /** @internal */\n  _joinRef() {\n    return this.joinPush.ref;\n  }\n  /** @internal */\n  _trigger(type, payload, ref) {\n    var _a, _b;\n    const typeLower = type.toLocaleLowerCase();\n    const {\n      close,\n      error,\n      leave,\n      join\n    } = CHANNEL_EVENTS;\n    const events = [close, error, leave, join];\n    if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n      return;\n    }\n    let handledPayload = this._onMessage(typeLower, payload, ref);\n    if (payload && !handledPayload) {\n      throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n    }\n    if (['insert', 'update', 'delete'].includes(typeLower)) {\n      (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter(bind => {\n        var _a, _b, _c;\n        return ((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' || ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower;\n      }).map(bind => bind.callback(handledPayload, ref));\n    } else {\n      (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter(bind => {\n        var _a, _b, _c, _d, _e, _f;\n        if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n          if ('id' in bind) {\n            const bindId = bind.id;\n            const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n            return bindId && ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) && (bindEvent === '*' || (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) === ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase()));\n          } else {\n            const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n            return bindEvent === '*' || bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase());\n          }\n        } else {\n          return bind.type.toLocaleLowerCase() === typeLower;\n        }\n      }).map(bind => {\n        if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n          const postgresChanges = handledPayload.data;\n          const {\n            schema,\n            table,\n            commit_timestamp,\n            type,\n            errors\n          } = postgresChanges;\n          const enrichedPayload = {\n            schema: schema,\n            table: table,\n            commit_timestamp: commit_timestamp,\n            eventType: type,\n            new: {},\n            old: {},\n            errors: errors\n          };\n          handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n        }\n        bind.callback(handledPayload, ref);\n      });\n    }\n  }\n  /** @internal */\n  _isClosed() {\n    return this.state === CHANNEL_STATES.closed;\n  }\n  /** @internal */\n  _isJoined() {\n    return this.state === CHANNEL_STATES.joined;\n  }\n  /** @internal */\n  _isJoining() {\n    return this.state === CHANNEL_STATES.joining;\n  }\n  /** @internal */\n  _isLeaving() {\n    return this.state === CHANNEL_STATES.leaving;\n  }\n  /** @internal */\n  _replyEventName(ref) {\n    return `chan_reply_${ref}`;\n  }\n  /** @internal */\n  _on(type, filter, callback) {\n    const typeLower = type.toLocaleLowerCase();\n    const binding = {\n      type: typeLower,\n      filter: filter,\n      callback: callback\n    };\n    if (this.bindings[typeLower]) {\n      this.bindings[typeLower].push(binding);\n    } else {\n      this.bindings[typeLower] = [binding];\n    }\n    return this;\n  }\n  /** @internal */\n  _off(type, filter) {\n    const typeLower = type.toLocaleLowerCase();\n    this.bindings[typeLower] = this.bindings[typeLower].filter(bind => {\n      var _a;\n      return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower && RealtimeChannel.isEqual(bind.filter, filter));\n    });\n    return this;\n  }\n  /** @internal */\n  static isEqual(obj1, obj2) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n      return false;\n    }\n    for (const k in obj1) {\n      if (obj1[k] !== obj2[k]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /** @internal */\n  _rejoinUntilConnected() {\n    this.rejoinTimer.scheduleTimeout();\n    if (this.socket.isConnected()) {\n      this._rejoin();\n    }\n  }\n  /**\n   * Registers a callback that will be executed when the channel closes.\n   *\n   * @internal\n   */\n  _onClose(callback) {\n    this._on(CHANNEL_EVENTS.close, {}, callback);\n  }\n  /**\n   * Registers a callback that will be executed when the channel encounteres an error.\n   *\n   * @internal\n   */\n  _onError(callback) {\n    this._on(CHANNEL_EVENTS.error, {}, reason => callback(reason));\n  }\n  /**\n   * Returns `true` if the socket is connected and the channel has been joined.\n   *\n   * @internal\n   */\n  _canPush() {\n    return this.socket.isConnected() && this._isJoined();\n  }\n  /** @internal */\n  _rejoin(timeout = this.timeout) {\n    if (this._isLeaving()) {\n      return;\n    }\n    this.socket._leaveOpenTopic(this.topic);\n    this.state = CHANNEL_STATES.joining;\n    this.joinPush.resend(timeout);\n  }\n  /** @internal */\n  _getPayloadRecords(payload) {\n    const records = {\n      new: {},\n      old: {}\n    };\n    if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n      records.new = Transformers.convertChangeData(payload.columns, payload.record);\n    }\n    if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n      records.old = Transformers.convertChangeData(payload.columns, payload.old_record);\n    }\n    return records;\n  }\n}", "map": {"version": 3, "names": ["CHANNEL_EVENTS", "CHANNEL_STATES", "<PERSON><PERSON>", "Timer", "RealtimePresence", "Transformers", "httpEndpointURL", "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "REALTIME_LISTEN_TYPES", "REALTIME_SUBSCRIBE_STATES", "REALTIME_CHANNEL_STATES", "RealtimeChannel", "constructor", "topic", "params", "config", "socket", "bindings", "state", "closed", "joinedOnce", "pushBuffer", "subTopic", "replace", "Object", "assign", "broadcast", "ack", "self", "presence", "key", "private", "timeout", "joinPush", "join", "rejoinTimer", "_rejoinUntilConnected", "reconnectAfterMs", "receive", "joined", "reset", "for<PERSON>ach", "pushEvent", "send", "_onClose", "log", "_joinRef", "_remove", "_onError", "reason", "_isLeaving", "_isClosed", "errored", "scheduleTimeout", "_isJoining", "_on", "reply", "payload", "ref", "_trigger", "_replyEventName", "broadcastEndpointURL", "endPoint", "subscribe", "callback", "isConnected", "connect", "isPrivate", "e", "CHANNEL_ERROR", "CLOSED", "accessTokenPayload", "postgres_changes", "_b", "_a", "map", "r", "filter", "accessTokenValue", "access_token", "updateJoinPayload", "_rejoin", "setAuth", "undefined", "SUBSCRIBED", "clientPostgresBindings", "bindingsLen", "length", "newPostgresBindings", "i", "clientPostgresBinding", "event", "schema", "table", "serverPostgresFilter", "push", "id", "unsubscribe", "Error", "error", "JSON", "stringify", "values", "TIMED_OUT", "presenceState", "track", "opts", "type", "untrack", "on", "args", "_canPush", "endpoint_payload", "authorization", "options", "method", "headers", "Authorization", "apikey", "<PERSON><PERSON><PERSON><PERSON>", "body", "messages", "response", "_fetchWithTimeout", "cancel", "ok", "name", "Promise", "resolve", "_push", "_c", "updatePayload", "leaving", "onClose", "close", "destroy", "leavePush", "leave", "trigger", "finally", "teardown", "clearTimeout", "timer", "url", "controller", "AbortController", "setTimeout", "abort", "fetch", "signal", "startTimeout", "_onMessage", "_event", "_ref", "_isMember", "typeLower", "toLocaleLowerCase", "events", "indexOf", "handledPayload", "includes", "bind", "bindId", "bindEvent", "ids", "data", "_e", "_d", "_f", "postgresChanges", "commit_timestamp", "errors", "enrichedPayload", "eventType", "new", "old", "_getPayloadRecords", "_isJoined", "joining", "binding", "_off", "isEqual", "obj1", "obj2", "keys", "k", "_leaveOpenTopic", "resend", "records", "convertChangeData", "columns", "record", "old_record"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\realtime-js\\src\\RealtimeChannel.ts"], "sourcesContent": ["import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants'\nimport Push from './lib/push'\nimport type RealtimeClient from './RealtimeClient'\nimport Timer from './lib/timer'\nimport RealtimePresence, {\n  REALTIME_PRESENCE_LISTEN_EVENTS,\n} from './RealtimePresence'\nimport type {\n  RealtimePresenceJoinPayload,\n  RealtimePresenceLeavePayload,\n  RealtimePresenceState,\n} from './RealtimePresence'\nimport * as Transformers from './lib/transformers'\nimport { httpEndpointURL } from './lib/transformers'\n\nexport type RealtimeChannelOptions = {\n  config: {\n    /**\n     * self option enables client to receive message it broadcast\n     * ack option instructs server to acknowledge that broadcast message was received\n     */\n    broadcast?: { self?: boolean; ack?: boolean }\n    /**\n     * key option is used to track presence payload across clients\n     */\n    presence?: { key?: string }\n    /**\n     * defines if the channel is private or not and if RLS policies will be used to check data\n     */\n    private?: boolean\n  }\n}\n\ntype RealtimePostgresChangesPayloadBase = {\n  schema: string\n  table: string\n  commit_timestamp: string\n  errors: string[]\n}\n\nexport type RealtimePostgresInsertPayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`\n    new: T\n    old: {}\n  }\n\nexport type RealtimePostgresUpdatePayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`\n    new: T\n    old: Partial<T>\n  }\n\nexport type RealtimePostgresDeletePayload<T extends { [key: string]: any }> =\n  RealtimePostgresChangesPayloadBase & {\n    eventType: `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE}`\n    new: {}\n    old: Partial<T>\n  }\n\nexport type RealtimePostgresChangesPayload<T extends { [key: string]: any }> =\n  | RealtimePostgresInsertPayload<T>\n  | RealtimePostgresUpdatePayload<T>\n  | RealtimePostgresDeletePayload<T>\n\nexport type RealtimePostgresChangesFilter<\n  T extends `${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT}`\n> = {\n  /**\n   * The type of database change to listen to.\n   */\n  event: T\n  /**\n   * The database schema to listen to.\n   */\n  schema: string\n  /**\n   * The database table to listen to.\n   */\n  table?: string\n  /**\n   * Receive database changes when filter is matched.\n   */\n  filter?: string\n}\n\nexport type RealtimeChannelSendResponse = 'ok' | 'timed out' | 'error'\n\nexport enum REALTIME_POSTGRES_CHANGES_LISTEN_EVENT {\n  ALL = '*',\n  INSERT = 'INSERT',\n  UPDATE = 'UPDATE',\n  DELETE = 'DELETE',\n}\n\nexport enum REALTIME_LISTEN_TYPES {\n  BROADCAST = 'broadcast',\n  PRESENCE = 'presence',\n  POSTGRES_CHANGES = 'postgres_changes',\n  SYSTEM = 'system',\n}\n\nexport enum REALTIME_SUBSCRIBE_STATES {\n  SUBSCRIBED = 'SUBSCRIBED',\n  TIMED_OUT = 'TIMED_OUT',\n  CLOSED = 'CLOSED',\n  CHANNEL_ERROR = 'CHANNEL_ERROR',\n}\n\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES\n\ninterface PostgresChangesFilters {\n  postgres_changes: {\n    id: string\n    event: string\n    schema?: string\n    table?: string\n    filter?: string\n  }[]\n}\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n  bindings: {\n    [key: string]: {\n      type: string\n      filter: { [key: string]: any }\n      callback: Function\n      id?: string\n    }[]\n  } = {}\n  timeout: number\n  state: CHANNEL_STATES = CHANNEL_STATES.closed\n  joinedOnce = false\n  joinPush: Push\n  rejoinTimer: Timer\n  pushBuffer: Push[] = []\n  presence: RealtimePresence\n  broadcastEndpointURL: string\n  subTopic: string\n  private: boolean\n\n  constructor(\n    /** Topic name can be any string. */\n    public topic: string,\n    public params: RealtimeChannelOptions = { config: {} },\n    public socket: RealtimeClient\n  ) {\n    this.subTopic = topic.replace(/^realtime:/i, '')\n    this.params.config = {\n      ...{\n        broadcast: { ack: false, self: false },\n        presence: { key: '' },\n        private: false,\n      },\n      ...params.config,\n    }\n    this.timeout = this.socket.timeout\n    this.joinPush = new Push(\n      this,\n      CHANNEL_EVENTS.join,\n      this.params,\n      this.timeout\n    )\n    this.rejoinTimer = new Timer(\n      () => this._rejoinUntilConnected(),\n      this.socket.reconnectAfterMs\n    )\n    this.joinPush.receive('ok', () => {\n      this.state = CHANNEL_STATES.joined\n      this.rejoinTimer.reset()\n      this.pushBuffer.forEach((pushEvent: Push) => pushEvent.send())\n      this.pushBuffer = []\n    })\n    this._onClose(() => {\n      this.rejoinTimer.reset()\n      this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`)\n      this.state = CHANNEL_STATES.closed\n      this.socket._remove(this)\n    })\n    this._onError((reason: string) => {\n      if (this._isLeaving() || this._isClosed()) {\n        return\n      }\n      this.socket.log('channel', `error ${this.topic}`, reason)\n      this.state = CHANNEL_STATES.errored\n      this.rejoinTimer.scheduleTimeout()\n    })\n    this.joinPush.receive('timeout', () => {\n      if (!this._isJoining()) {\n        return\n      }\n      this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout)\n      this.state = CHANNEL_STATES.errored\n      this.rejoinTimer.scheduleTimeout()\n    })\n    this._on(CHANNEL_EVENTS.reply, {}, (payload: any, ref: string) => {\n      this._trigger(this._replyEventName(ref), payload)\n    })\n\n    this.presence = new RealtimePresence(this)\n\n    this.broadcastEndpointURL =\n      httpEndpointURL(this.socket.endPoint) + '/api/broadcast'\n    this.private = this.params.config.private || false\n  }\n\n  /** Subscribe registers your client with the server */\n  subscribe(\n    callback?: (status: REALTIME_SUBSCRIBE_STATES, err?: Error) => void,\n    timeout = this.timeout\n  ): RealtimeChannel {\n    if (!this.socket.isConnected()) {\n      this.socket.connect()\n    }\n    if (this.state == CHANNEL_STATES.closed) {\n      const {\n        config: { broadcast, presence, private: isPrivate },\n      } = this.params\n\n      this._onError((e: Error) =>\n        callback?.(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e)\n      )\n      this._onClose(() => callback?.(REALTIME_SUBSCRIBE_STATES.CLOSED))\n\n      const accessTokenPayload: { access_token?: string } = {}\n      const config = {\n        broadcast,\n        presence,\n        postgres_changes:\n          this.bindings.postgres_changes?.map((r) => r.filter) ?? [],\n        private: isPrivate,\n      }\n\n      if (this.socket.accessTokenValue) {\n        accessTokenPayload.access_token = this.socket.accessTokenValue\n      }\n\n      this.updateJoinPayload({ ...{ config }, ...accessTokenPayload })\n\n      this.joinedOnce = true\n      this._rejoin(timeout)\n\n      this.joinPush\n        .receive('ok', async ({ postgres_changes }: PostgresChangesFilters) => {\n          this.socket.setAuth()\n          if (postgres_changes === undefined) {\n            callback?.(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED)\n            return\n          } else {\n            const clientPostgresBindings = this.bindings.postgres_changes\n            const bindingsLen = clientPostgresBindings?.length ?? 0\n            const newPostgresBindings = []\n\n            for (let i = 0; i < bindingsLen; i++) {\n              const clientPostgresBinding = clientPostgresBindings[i]\n              const {\n                filter: { event, schema, table, filter },\n              } = clientPostgresBinding\n              const serverPostgresFilter =\n                postgres_changes && postgres_changes[i]\n\n              if (\n                serverPostgresFilter &&\n                serverPostgresFilter.event === event &&\n                serverPostgresFilter.schema === schema &&\n                serverPostgresFilter.table === table &&\n                serverPostgresFilter.filter === filter\n              ) {\n                newPostgresBindings.push({\n                  ...clientPostgresBinding,\n                  id: serverPostgresFilter.id,\n                })\n              } else {\n                this.unsubscribe()\n                this.state = CHANNEL_STATES.errored\n\n                callback?.(\n                  REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR,\n                  new Error(\n                    'mismatch between server and client bindings for postgres changes'\n                  )\n                )\n                return\n              }\n            }\n\n            this.bindings.postgres_changes = newPostgresBindings\n\n            callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED)\n            return\n          }\n        })\n        .receive('error', (error: { [key: string]: any }) => {\n          this.state = CHANNEL_STATES.errored\n          callback?.(\n            REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR,\n            new Error(\n              JSON.stringify(Object.values(error).join(', ') || 'error')\n            )\n          )\n          return\n        })\n        .receive('timeout', () => {\n          callback?.(REALTIME_SUBSCRIBE_STATES.TIMED_OUT)\n          return\n        })\n    }\n    return this\n  }\n\n  presenceState<\n    T extends { [key: string]: any } = {}\n  >(): RealtimePresenceState<T> {\n    return this.presence.state as RealtimePresenceState<T>\n  }\n\n  async track(\n    payload: { [key: string]: any },\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    return await this.send(\n      {\n        type: 'presence',\n        event: 'track',\n        payload,\n      },\n      opts.timeout || this.timeout\n    )\n  }\n\n  async untrack(\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    return await this.send(\n      {\n        type: 'presence',\n        event: 'untrack',\n      },\n      opts\n    )\n  }\n\n  /**\n   * Creates an event handler that listens to changes.\n   */\n  on(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.SYNC}` },\n    callback: () => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}` },\n    callback: (payload: RealtimePresenceJoinPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.PRESENCE}`,\n    filter: { event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}` },\n    callback: (payload: RealtimePresenceLeavePayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.ALL}`>,\n    callback: (payload: RealtimePostgresChangesPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT}`>,\n    callback: (payload: RealtimePostgresInsertPayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE}`>,\n    callback: (payload: RealtimePostgresUpdatePayload<T>) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.POSTGRES_CHANGES}`,\n    filter: RealtimePostgresChangesFilter<`${REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE}`>,\n    callback: (payload: RealtimePostgresDeletePayload<T>) => void\n  ): RealtimeChannel\n  /**\n   * The following is placed here to display on supabase.com/docs/reference/javascript/subscribe.\n   * @param type One of \"broadcast\", \"presence\", or \"postgres_changes\".\n   * @param filter Custom object specific to the Realtime feature detailing which payloads to receive.\n   * @param callback Function to be invoked when event handler is triggered.\n   */\n  on(\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`,\n    filter: { event: string },\n    callback: (payload: {\n      type: `${REALTIME_LISTEN_TYPES.BROADCAST}`\n      event: string\n      [key: string]: any\n    }) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.BROADCAST}`,\n    filter: { event: string },\n    callback: (payload: {\n      type: `${REALTIME_LISTEN_TYPES.BROADCAST}`\n      event: string\n      payload: T\n    }) => void\n  ): RealtimeChannel\n  on<T extends { [key: string]: any }>(\n    type: `${REALTIME_LISTEN_TYPES.SYSTEM}`,\n    filter: {},\n    callback: (payload: any) => void\n  ): RealtimeChannel\n  on(\n    type: `${REALTIME_LISTEN_TYPES}`,\n    filter: { event: string; [key: string]: string },\n    callback: (payload: any) => void\n  ): RealtimeChannel {\n    return this._on(type, filter, callback)\n  }\n  /**\n   * Sends a message into the channel.\n   *\n   * @param args Arguments to send to channel\n   * @param args.type The type of event to send\n   * @param args.event The name of the event being sent\n   * @param args.payload Payload to be sent\n   * @param opts Options to be used during the send process\n   */\n  async send(\n    args: {\n      type: 'broadcast' | 'presence' | 'postgres_changes'\n      event: string\n      payload?: any\n      [key: string]: any\n    },\n    opts: { [key: string]: any } = {}\n  ): Promise<RealtimeChannelSendResponse> {\n    if (!this._canPush() && args.type === 'broadcast') {\n      const { event, payload: endpoint_payload } = args\n      const authorization = this.socket.accessTokenValue\n        ? `Bearer ${this.socket.accessTokenValue}`\n        : ''\n      const options = {\n        method: 'POST',\n        headers: {\n          Authorization: authorization,\n          apikey: this.socket.apiKey ? this.socket.apiKey : '',\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          messages: [\n            {\n              topic: this.subTopic,\n              event,\n              payload: endpoint_payload,\n              private: this.private,\n            },\n          ],\n        }),\n      }\n\n      try {\n        const response = await this._fetchWithTimeout(\n          this.broadcastEndpointURL,\n          options,\n          opts.timeout ?? this.timeout\n        )\n\n        await response.body?.cancel()\n        return response.ok ? 'ok' : 'error'\n      } catch (error: any) {\n        if (error.name === 'AbortError') {\n          return 'timed out'\n        } else {\n          return 'error'\n        }\n      }\n    } else {\n      return new Promise((resolve) => {\n        const push = this._push(args.type, args, opts.timeout || this.timeout)\n\n        if (args.type === 'broadcast' && !this.params?.config?.broadcast?.ack) {\n          resolve('ok')\n        }\n\n        push.receive('ok', () => resolve('ok'))\n        push.receive('error', () => resolve('error'))\n        push.receive('timeout', () => resolve('timed out'))\n      })\n    }\n  }\n\n  updateJoinPayload(payload: { [key: string]: any }): void {\n    this.joinPush.updatePayload(payload)\n  }\n\n  /**\n   * Leaves the channel.\n   *\n   * Unsubscribes from server events, and instructs channel to terminate on server.\n   * Triggers onClose() hooks.\n   *\n   * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n   * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n   */\n  unsubscribe(timeout = this.timeout): Promise<'ok' | 'timed out' | 'error'> {\n    this.state = CHANNEL_STATES.leaving\n    const onClose = () => {\n      this.socket.log('channel', `leave ${this.topic}`)\n      this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef())\n    }\n\n    this.joinPush.destroy()\n\n    let leavePush: Push | null = null\n\n    return new Promise<RealtimeChannelSendResponse>((resolve) => {\n      leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout)\n      leavePush\n        .receive('ok', () => {\n          onClose()\n          resolve('ok')\n        })\n        .receive('timeout', () => {\n          onClose()\n          resolve('timed out')\n        })\n        .receive('error', () => {\n          resolve('error')\n        })\n\n      leavePush.send()\n      if (!this._canPush()) {\n        leavePush.trigger('ok', {})\n      }\n    }).finally(() => {\n      leavePush?.destroy()\n    })\n  }\n  /**\n   * Teardown the channel.\n   *\n   * Destroys and stops related timers.\n   */\n  teardown() {\n    this.pushBuffer.forEach((push: Push) => push.destroy())\n    this.rejoinTimer && clearTimeout(this.rejoinTimer.timer)\n    this.joinPush.destroy()\n  }\n\n  /** @internal */\n\n  async _fetchWithTimeout(\n    url: string,\n    options: { [key: string]: any },\n    timeout: number\n  ) {\n    const controller = new AbortController()\n    const id = setTimeout(() => controller.abort(), timeout)\n\n    const response = await this.socket.fetch(url, {\n      ...options,\n      signal: controller.signal,\n    })\n\n    clearTimeout(id)\n\n    return response\n  }\n\n  /** @internal */\n  _push(\n    event: string,\n    payload: { [key: string]: any },\n    timeout = this.timeout\n  ) {\n    if (!this.joinedOnce) {\n      throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`\n    }\n    let pushEvent = new Push(this, event, payload, timeout)\n    if (this._canPush()) {\n      pushEvent.send()\n    } else {\n      pushEvent.startTimeout()\n      this.pushBuffer.push(pushEvent)\n    }\n\n    return pushEvent\n  }\n\n  /**\n   * Overridable message hook\n   *\n   * Receives all events for specialized message handling before dispatching to the channel callbacks.\n   * Must return the payload, modified or unmodified.\n   *\n   * @internal\n   */\n  _onMessage(_event: string, payload: any, _ref?: string) {\n    return payload\n  }\n\n  /** @internal */\n  _isMember(topic: string): boolean {\n    return this.topic === topic\n  }\n\n  /** @internal */\n  _joinRef(): string {\n    return this.joinPush.ref\n  }\n\n  /** @internal */\n  _trigger(type: string, payload?: any, ref?: string) {\n    const typeLower = type.toLocaleLowerCase()\n    const { close, error, leave, join } = CHANNEL_EVENTS\n    const events: string[] = [close, error, leave, join]\n    if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n      return\n    }\n    let handledPayload = this._onMessage(typeLower, payload, ref)\n    if (payload && !handledPayload) {\n      throw 'channel onMessage callbacks must return the payload, modified or unmodified'\n    }\n\n    if (['insert', 'update', 'delete'].includes(typeLower)) {\n      this.bindings.postgres_changes\n        ?.filter((bind) => {\n          return (\n            bind.filter?.event === '*' ||\n            bind.filter?.event?.toLocaleLowerCase() === typeLower\n          )\n        })\n        .map((bind) => bind.callback(handledPayload, ref))\n    } else {\n      this.bindings[typeLower]\n        ?.filter((bind) => {\n          if (\n            ['broadcast', 'presence', 'postgres_changes'].includes(typeLower)\n          ) {\n            if ('id' in bind) {\n              const bindId = bind.id\n              const bindEvent = bind.filter?.event\n              return (\n                bindId &&\n                payload.ids?.includes(bindId) &&\n                (bindEvent === '*' ||\n                  bindEvent?.toLocaleLowerCase() ===\n                    payload.data?.type.toLocaleLowerCase())\n              )\n            } else {\n              const bindEvent = bind?.filter?.event?.toLocaleLowerCase()\n              return (\n                bindEvent === '*' ||\n                bindEvent === payload?.event?.toLocaleLowerCase()\n              )\n            }\n          } else {\n            return bind.type.toLocaleLowerCase() === typeLower\n          }\n        })\n        .map((bind) => {\n          if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n            const postgresChanges = handledPayload.data\n            const { schema, table, commit_timestamp, type, errors } =\n              postgresChanges\n            const enrichedPayload = {\n              schema: schema,\n              table: table,\n              commit_timestamp: commit_timestamp,\n              eventType: type,\n              new: {},\n              old: {},\n              errors: errors,\n            }\n            handledPayload = {\n              ...enrichedPayload,\n              ...this._getPayloadRecords(postgresChanges),\n            }\n          }\n          bind.callback(handledPayload, ref)\n        })\n    }\n  }\n\n  /** @internal */\n  _isClosed(): boolean {\n    return this.state === CHANNEL_STATES.closed\n  }\n\n  /** @internal */\n  _isJoined(): boolean {\n    return this.state === CHANNEL_STATES.joined\n  }\n\n  /** @internal */\n  _isJoining(): boolean {\n    return this.state === CHANNEL_STATES.joining\n  }\n\n  /** @internal */\n  _isLeaving(): boolean {\n    return this.state === CHANNEL_STATES.leaving\n  }\n\n  /** @internal */\n  _replyEventName(ref: string): string {\n    return `chan_reply_${ref}`\n  }\n\n  /** @internal */\n  _on(type: string, filter: { [key: string]: any }, callback: Function) {\n    const typeLower = type.toLocaleLowerCase()\n\n    const binding = {\n      type: typeLower,\n      filter: filter,\n      callback: callback,\n    }\n\n    if (this.bindings[typeLower]) {\n      this.bindings[typeLower].push(binding)\n    } else {\n      this.bindings[typeLower] = [binding]\n    }\n\n    return this\n  }\n\n  /** @internal */\n  _off(type: string, filter: { [key: string]: any }) {\n    const typeLower = type.toLocaleLowerCase()\n\n    this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n      return !(\n        bind.type?.toLocaleLowerCase() === typeLower &&\n        RealtimeChannel.isEqual(bind.filter, filter)\n      )\n    })\n    return this\n  }\n\n  /** @internal */\n  private static isEqual(\n    obj1: { [key: string]: string },\n    obj2: { [key: string]: string }\n  ) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n      return false\n    }\n\n    for (const k in obj1) {\n      if (obj1[k] !== obj2[k]) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  /** @internal */\n  private _rejoinUntilConnected() {\n    this.rejoinTimer.scheduleTimeout()\n    if (this.socket.isConnected()) {\n      this._rejoin()\n    }\n  }\n\n  /**\n   * Registers a callback that will be executed when the channel closes.\n   *\n   * @internal\n   */\n  private _onClose(callback: Function) {\n    this._on(CHANNEL_EVENTS.close, {}, callback)\n  }\n\n  /**\n   * Registers a callback that will be executed when the channel encounteres an error.\n   *\n   * @internal\n   */\n  private _onError(callback: Function) {\n    this._on(CHANNEL_EVENTS.error, {}, (reason: string) => callback(reason))\n  }\n\n  /**\n   * Returns `true` if the socket is connected and the channel has been joined.\n   *\n   * @internal\n   */\n  private _canPush(): boolean {\n    return this.socket.isConnected() && this._isJoined()\n  }\n\n  /** @internal */\n  private _rejoin(timeout = this.timeout): void {\n    if (this._isLeaving()) {\n      return\n    }\n    this.socket._leaveOpenTopic(this.topic)\n    this.state = CHANNEL_STATES.joining\n    this.joinPush.resend(timeout)\n  }\n\n  /** @internal */\n  private _getPayloadRecords(payload: any) {\n    const records = {\n      new: {},\n      old: {},\n    }\n\n    if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n      records.new = Transformers.convertChangeData(\n        payload.columns,\n        payload.record\n      )\n    }\n\n    if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n      records.old = Transformers.convertChangeData(\n        payload.columns,\n        payload.old_record\n      )\n    }\n\n    return records\n  }\n}\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,cAAc,QAAQ,iBAAiB;AAChE,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,gBAEN,MAAM,oBAAoB;AAM3B,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,SAASC,eAAe,QAAQ,oBAAoB;AA4EpD,WAAYC,sCAKX;AALD,WAAYA,sCAAsC;EAChDA,sCAAA,aAAS;EACTA,sCAAA,qBAAiB;EACjBA,sCAAA,qBAAiB;EACjBA,sCAAA,qBAAiB;AACnB,CAAC,EALWA,sCAAsC,KAAtCA,sCAAsC;AAOlD,WAAYC,qBAKX;AALD,WAAYA,qBAAqB;EAC/BA,qBAAA,2BAAuB;EACvBA,qBAAA,yBAAqB;EACrBA,qBAAA,yCAAqC;EACrCA,qBAAA,qBAAiB;AACnB,CAAC,EALWA,qBAAqB,KAArBA,qBAAqB;AAOjC,WAAYC,yBAKX;AALD,WAAYA,yBAAyB;EACnCA,yBAAA,6BAAyB;EACzBA,yBAAA,2BAAuB;EACvBA,yBAAA,qBAAiB;EACjBA,yBAAA,mCAA+B;AACjC,CAAC,EALWA,yBAAyB,KAAzBA,yBAAyB;AAOrC,OAAO,MAAMC,uBAAuB,GAAGT,cAAc;AAWrD;;;;;AAKA,eAAc,MAAOU,eAAe;EAoBlCC,YACE;EACOC,KAAa,EACbC,MAAA,GAAiC;IAAEC,MAAM,EAAE;EAAE,CAAE,EAC/CC,MAAsB;IAFtB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAE,MAAM,GAANA,MAAM;IAvBf,KAAAC,QAAQ,GAOJ,EAAE;IAEN,KAAAC,KAAK,GAAmBjB,cAAc,CAACkB,MAAM;IAC7C,KAAAC,UAAU,GAAG,KAAK;IAGlB,KAAAC,UAAU,GAAW,EAAE;IAYrB,IAAI,CAACC,QAAQ,GAAGT,KAAK,CAACU,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;IAChD,IAAI,CAACT,MAAM,CAACC,MAAM,GAAAS,MAAA,CAAAC,MAAA,CACb;MACDC,SAAS,EAAE;QAAEC,GAAG,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAE;MACtCC,QAAQ,EAAE;QAAEC,GAAG,EAAE;MAAE,CAAE;MACrBC,OAAO,EAAE;KACV,EACEjB,MAAM,CAACC,MAAM,CACjB;IACD,IAAI,CAACiB,OAAO,GAAG,IAAI,CAAChB,MAAM,CAACgB,OAAO;IAClC,IAAI,CAACC,QAAQ,GAAG,IAAI/B,IAAI,CACtB,IAAI,EACJF,cAAc,CAACkC,IAAI,EACnB,IAAI,CAACpB,MAAM,EACX,IAAI,CAACkB,OAAO,CACb;IACD,IAAI,CAACG,WAAW,GAAG,IAAIhC,KAAK,CAC1B,MAAM,IAAI,CAACiC,qBAAqB,EAAE,EAClC,IAAI,CAACpB,MAAM,CAACqB,gBAAgB,CAC7B;IACD,IAAI,CAACJ,QAAQ,CAACK,OAAO,CAAC,IAAI,EAAE,MAAK;MAC/B,IAAI,CAACpB,KAAK,GAAGjB,cAAc,CAACsC,MAAM;MAClC,IAAI,CAACJ,WAAW,CAACK,KAAK,EAAE;MACxB,IAAI,CAACnB,UAAU,CAACoB,OAAO,CAAEC,SAAe,IAAKA,SAAS,CAACC,IAAI,EAAE,CAAC;MAC9D,IAAI,CAACtB,UAAU,GAAG,EAAE;IACtB,CAAC,CAAC;IACF,IAAI,CAACuB,QAAQ,CAAC,MAAK;MACjB,IAAI,CAACT,WAAW,CAACK,KAAK,EAAE;MACxB,IAAI,CAACxB,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAChC,KAAK,IAAI,IAAI,CAACiC,QAAQ,EAAE,EAAE,CAAC;MACpE,IAAI,CAAC5B,KAAK,GAAGjB,cAAc,CAACkB,MAAM;MAClC,IAAI,CAACH,MAAM,CAAC+B,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,CAAEC,MAAc,IAAI;MAC/B,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;QACzC;MACF;MACA,IAAI,CAACnC,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAChC,KAAK,EAAE,EAAEoC,MAAM,CAAC;MACzD,IAAI,CAAC/B,KAAK,GAAGjB,cAAc,CAACmD,OAAO;MACnC,IAAI,CAACjB,WAAW,CAACkB,eAAe,EAAE;IACpC,CAAC,CAAC;IACF,IAAI,CAACpB,QAAQ,CAACK,OAAO,CAAC,SAAS,EAAE,MAAK;MACpC,IAAI,CAAC,IAAI,CAACgB,UAAU,EAAE,EAAE;QACtB;MACF;MACA,IAAI,CAACtC,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,WAAW,IAAI,CAAChC,KAAK,EAAE,EAAE,IAAI,CAACoB,QAAQ,CAACD,OAAO,CAAC;MAC1E,IAAI,CAACd,KAAK,GAAGjB,cAAc,CAACmD,OAAO;MACnC,IAAI,CAACjB,WAAW,CAACkB,eAAe,EAAE;IACpC,CAAC,CAAC;IACF,IAAI,CAACE,GAAG,CAACvD,cAAc,CAACwD,KAAK,EAAE,EAAE,EAAE,CAACC,OAAY,EAAEC,GAAW,KAAI;MAC/D,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACC,eAAe,CAACF,GAAG,CAAC,EAAED,OAAO,CAAC;IACnD,CAAC,CAAC;IAEF,IAAI,CAAC5B,QAAQ,GAAG,IAAIzB,gBAAgB,CAAC,IAAI,CAAC;IAE1C,IAAI,CAACyD,oBAAoB,GACvBvD,eAAe,CAAC,IAAI,CAACU,MAAM,CAAC8C,QAAQ,CAAC,GAAG,gBAAgB;IAC1D,IAAI,CAAC/B,OAAO,GAAG,IAAI,CAACjB,MAAM,CAACC,MAAM,CAACgB,OAAO,IAAI,KAAK;EACpD;EAEA;EACAgC,SAASA,CACPC,QAAmE,EACnEhC,OAAO,GAAG,IAAI,CAACA,OAAO;;IAEtB,IAAI,CAAC,IAAI,CAAChB,MAAM,CAACiD,WAAW,EAAE,EAAE;MAC9B,IAAI,CAACjD,MAAM,CAACkD,OAAO,EAAE;IACvB;IACA,IAAI,IAAI,CAAChD,KAAK,IAAIjB,cAAc,CAACkB,MAAM,EAAE;MACvC,MAAM;QACJJ,MAAM,EAAE;UAAEW,SAAS;UAAEG,QAAQ;UAAEE,OAAO,EAAEoC;QAAS;MAAE,CACpD,GAAG,IAAI,CAACrD,MAAM;MAEf,IAAI,CAACkC,QAAQ,CAAEoB,CAAQ,IACrBJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGvD,yBAAyB,CAAC4D,aAAa,EAAED,CAAC,CAAC,CACvD;MACD,IAAI,CAACxB,QAAQ,CAAC,MAAMoB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGvD,yBAAyB,CAAC6D,MAAM,CAAC,CAAC;MAEjE,MAAMC,kBAAkB,GAA8B,EAAE;MACxD,MAAMxD,MAAM,GAAG;QACbW,SAAS;QACTG,QAAQ;QACR2C,gBAAgB,EACd,CAAAC,EAAA,IAAAC,EAAA,OAAI,CAACzD,QAAQ,CAACuD,gBAAgB,cAAAE,EAAA,uBAAAA,EAAA,CAAEC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC,cAAAJ,EAAA,cAAAA,EAAA,GAAI,EAAE;QAC5D1C,OAAO,EAAEoC;OACV;MAED,IAAI,IAAI,CAACnD,MAAM,CAAC8D,gBAAgB,EAAE;QAChCP,kBAAkB,CAACQ,YAAY,GAAG,IAAI,CAAC/D,MAAM,CAAC8D,gBAAgB;MAChE;MAEA,IAAI,CAACE,iBAAiB,CAAAxD,MAAA,CAAAC,MAAA,CAAM;QAAEV;MAAM,CAAE,EAAKwD,kBAAkB,EAAG;MAEhE,IAAI,CAACnD,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6D,OAAO,CAACjD,OAAO,CAAC;MAErB,IAAI,CAACC,QAAQ,CACVK,OAAO,CAAC,IAAI,EAAE,OAAO;QAAEkC;MAAgB,CAA0B,KAAI;;QACpE,IAAI,CAACxD,MAAM,CAACkE,OAAO,EAAE;QACrB,IAAIV,gBAAgB,KAAKW,SAAS,EAAE;UAClCnB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGvD,yBAAyB,CAAC2E,UAAU,CAAC;UAChD;QACF,CAAC,MAAM;UACL,MAAMC,sBAAsB,GAAG,IAAI,CAACpE,QAAQ,CAACuD,gBAAgB;UAC7D,MAAMc,WAAW,GAAG,CAAAZ,EAAA,GAAAW,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEE,MAAM,cAAAb,EAAA,cAAAA,EAAA,GAAI,CAAC;UACvD,MAAMc,mBAAmB,GAAG,EAAE;UAE9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;YACpC,MAAMC,qBAAqB,GAAGL,sBAAsB,CAACI,CAAC,CAAC;YACvD,MAAM;cACJZ,MAAM,EAAE;gBAAEc,KAAK;gBAAEC,MAAM;gBAAEC,KAAK;gBAAEhB;cAAM;YAAE,CACzC,GAAGa,qBAAqB;YACzB,MAAMI,oBAAoB,GACxBtB,gBAAgB,IAAIA,gBAAgB,CAACiB,CAAC,CAAC;YAEzC,IACEK,oBAAoB,IACpBA,oBAAoB,CAACH,KAAK,KAAKA,KAAK,IACpCG,oBAAoB,CAACF,MAAM,KAAKA,MAAM,IACtCE,oBAAoB,CAACD,KAAK,KAAKA,KAAK,IACpCC,oBAAoB,CAACjB,MAAM,KAAKA,MAAM,EACtC;cACAW,mBAAmB,CAACO,IAAI,CAAAvE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACnBiE,qBAAqB;gBACxBM,EAAE,EAAEF,oBAAoB,CAACE;cAAE,GAC3B;YACJ,CAAC,MAAM;cACL,IAAI,CAACC,WAAW,EAAE;cAClB,IAAI,CAAC/E,KAAK,GAAGjB,cAAc,CAACmD,OAAO;cAEnCY,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CACNvD,yBAAyB,CAAC4D,aAAa,EACvC,IAAI6B,KAAK,CACP,kEAAkE,CACnE,CACF;cACD;YACF;UACF;UAEA,IAAI,CAACjF,QAAQ,CAACuD,gBAAgB,GAAGgB,mBAAmB;UAEpDxB,QAAQ,IAAIA,QAAQ,CAACvD,yBAAyB,CAAC2E,UAAU,CAAC;UAC1D;QACF;MACF,CAAC,CAAC,CACD9C,OAAO,CAAC,OAAO,EAAG6D,KAA6B,IAAI;QAClD,IAAI,CAACjF,KAAK,GAAGjB,cAAc,CAACmD,OAAO;QACnCY,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CACNvD,yBAAyB,CAAC4D,aAAa,EACvC,IAAI6B,KAAK,CACPE,IAAI,CAACC,SAAS,CAAC7E,MAAM,CAAC8E,MAAM,CAACH,KAAK,CAAC,CAACjE,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAC3D,CACF;QACD;MACF,CAAC,CAAC,CACDI,OAAO,CAAC,SAAS,EAAE,MAAK;QACvB0B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGvD,yBAAyB,CAAC8F,SAAS,CAAC;QAC/C;MACF,CAAC,CAAC;IACN;IACA,OAAO,IAAI;EACb;EAEAC,aAAaA,CAAA;IAGX,OAAO,IAAI,CAAC3E,QAAQ,CAACX,KAAiC;EACxD;EAEA,MAAMuF,KAAKA,CACThD,OAA+B,EAC/BiD,IAAA,GAA+B,EAAE;IAEjC,OAAO,MAAM,IAAI,CAAC/D,IAAI,CACpB;MACEgE,IAAI,EAAE,UAAU;MAChBhB,KAAK,EAAE,OAAO;MACdlC;KACD,EACDiD,IAAI,CAAC1E,OAAO,IAAI,IAAI,CAACA,OAAO,CAC7B;EACH;EAEA,MAAM4E,OAAOA,CACXF,IAAA,GAA+B,EAAE;IAEjC,OAAO,MAAM,IAAI,CAAC/D,IAAI,CACpB;MACEgE,IAAI,EAAE,UAAU;MAChBhB,KAAK,EAAE;KACR,EACDe,IAAI,CACL;EACH;EAqEAG,EAAEA,CACAF,IAAgC,EAChC9B,MAAgD,EAChDb,QAAgC;IAEhC,OAAO,IAAI,CAACT,GAAG,CAACoD,IAAI,EAAE9B,MAAM,EAAEb,QAAQ,CAAC;EACzC;EACA;;;;;;;;;EASA,MAAMrB,IAAIA,CACRmE,IAKC,EACDJ,IAAA,GAA+B,EAAE;;IAEjC,IAAI,CAAC,IAAI,CAACK,QAAQ,EAAE,IAAID,IAAI,CAACH,IAAI,KAAK,WAAW,EAAE;MACjD,MAAM;QAAEhB,KAAK;QAAElC,OAAO,EAAEuD;MAAgB,CAAE,GAAGF,IAAI;MACjD,MAAMG,aAAa,GAAG,IAAI,CAACjG,MAAM,CAAC8D,gBAAgB,GAC9C,UAAU,IAAI,CAAC9D,MAAM,CAAC8D,gBAAgB,EAAE,GACxC,EAAE;MACN,MAAMoC,OAAO,GAAG;QACdC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACPC,aAAa,EAAEJ,aAAa;UAC5BK,MAAM,EAAE,IAAI,CAACtG,MAAM,CAACuG,MAAM,GAAG,IAAI,CAACvG,MAAM,CAACuG,MAAM,GAAG,EAAE;UACpD,cAAc,EAAE;SACjB;QACDC,IAAI,EAAEpB,IAAI,CAACC,SAAS,CAAC;UACnBoB,QAAQ,EAAE,CACR;YACE5G,KAAK,EAAE,IAAI,CAACS,QAAQ;YACpBqE,KAAK;YACLlC,OAAO,EAAEuD,gBAAgB;YACzBjF,OAAO,EAAE,IAAI,CAACA;WACf;SAEJ;OACF;MAED,IAAI;QACF,MAAM2F,QAAQ,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAC3C,IAAI,CAAC9D,oBAAoB,EACzBqD,OAAO,EACP,CAAAxC,EAAA,GAAAgC,IAAI,CAAC1E,OAAO,cAAA0C,EAAA,cAAAA,EAAA,GAAI,IAAI,CAAC1C,OAAO,CAC7B;QAED,OAAM,CAAAyC,EAAA,GAAAiD,QAAQ,CAACF,IAAI,cAAA/C,EAAA,uBAAAA,EAAA,CAAEmD,MAAM,EAAE;QAC7B,OAAOF,QAAQ,CAACG,EAAE,GAAG,IAAI,GAAG,OAAO;MACrC,CAAC,CAAC,OAAO1B,KAAU,EAAE;QACnB,IAAIA,KAAK,CAAC2B,IAAI,KAAK,YAAY,EAAE;UAC/B,OAAO,WAAW;QACpB,CAAC,MAAM;UACL,OAAO,OAAO;QAChB;MACF;IACF,CAAC,MAAM;MACL,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;;QAC7B,MAAMjC,IAAI,GAAG,IAAI,CAACkC,KAAK,CAACnB,IAAI,CAACH,IAAI,EAAEG,IAAI,EAAEJ,IAAI,CAAC1E,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC;QAEtE,IAAI8E,IAAI,CAACH,IAAI,KAAK,WAAW,IAAI,EAAC,CAAAuB,EAAA,IAAAzD,EAAA,IAAAC,EAAA,OAAI,CAAC5D,MAAM,cAAA4D,EAAA,uBAAAA,EAAA,CAAE3D,MAAM,cAAA0D,EAAA,uBAAAA,EAAA,CAAE/C,SAAS,cAAAwG,EAAA,uBAAAA,EAAA,CAAEvG,GAAG,GAAE;UACrEqG,OAAO,CAAC,IAAI,CAAC;QACf;QAEAjC,IAAI,CAACzD,OAAO,CAAC,IAAI,EAAE,MAAM0F,OAAO,CAAC,IAAI,CAAC,CAAC;QACvCjC,IAAI,CAACzD,OAAO,CAAC,OAAO,EAAE,MAAM0F,OAAO,CAAC,OAAO,CAAC,CAAC;QAC7CjC,IAAI,CAACzD,OAAO,CAAC,SAAS,EAAE,MAAM0F,OAAO,CAAC,WAAW,CAAC,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEAhD,iBAAiBA,CAACvB,OAA+B;IAC/C,IAAI,CAACxB,QAAQ,CAACkG,aAAa,CAAC1E,OAAO,CAAC;EACtC;EAEA;;;;;;;;;EASAwC,WAAWA,CAACjE,OAAO,GAAG,IAAI,CAACA,OAAO;IAChC,IAAI,CAACd,KAAK,GAAGjB,cAAc,CAACmI,OAAO;IACnC,MAAMC,OAAO,GAAGA,CAAA,KAAK;MACnB,IAAI,CAACrH,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAChC,KAAK,EAAE,CAAC;MACjD,IAAI,CAAC8C,QAAQ,CAAC3D,cAAc,CAACsI,KAAK,EAAE,OAAO,EAAE,IAAI,CAACxF,QAAQ,EAAE,CAAC;IAC/D,CAAC;IAED,IAAI,CAACb,QAAQ,CAACsG,OAAO,EAAE;IAEvB,IAAIC,SAAS,GAAgB,IAAI;IAEjC,OAAO,IAAIT,OAAO,CAA+BC,OAAO,IAAI;MAC1DQ,SAAS,GAAG,IAAItI,IAAI,CAAC,IAAI,EAAEF,cAAc,CAACyI,KAAK,EAAE,EAAE,EAAEzG,OAAO,CAAC;MAC7DwG,SAAS,CACNlG,OAAO,CAAC,IAAI,EAAE,MAAK;QAClB+F,OAAO,EAAE;QACTL,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,CAAC,CACD1F,OAAO,CAAC,SAAS,EAAE,MAAK;QACvB+F,OAAO,EAAE;QACTL,OAAO,CAAC,WAAW,CAAC;MACtB,CAAC,CAAC,CACD1F,OAAO,CAAC,OAAO,EAAE,MAAK;QACrB0F,OAAO,CAAC,OAAO,CAAC;MAClB,CAAC,CAAC;MAEJQ,SAAS,CAAC7F,IAAI,EAAE;MAChB,IAAI,CAAC,IAAI,CAACoE,QAAQ,EAAE,EAAE;QACpByB,SAAS,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;MAC7B;IACF,CAAC,CAAC,CAACC,OAAO,CAAC,MAAK;MACdH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,OAAO,EAAE;IACtB,CAAC,CAAC;EACJ;EACA;;;;;EAKAK,QAAQA,CAAA;IACN,IAAI,CAACvH,UAAU,CAACoB,OAAO,CAAEsD,IAAU,IAAKA,IAAI,CAACwC,OAAO,EAAE,CAAC;IACvD,IAAI,CAACpG,WAAW,IAAI0G,YAAY,CAAC,IAAI,CAAC1G,WAAW,CAAC2G,KAAK,CAAC;IACxD,IAAI,CAAC7G,QAAQ,CAACsG,OAAO,EAAE;EACzB;EAEA;EAEA,MAAMZ,iBAAiBA,CACrBoB,GAAW,EACX7B,OAA+B,EAC/BlF,OAAe;IAEf,MAAMgH,UAAU,GAAG,IAAIC,eAAe,EAAE;IACxC,MAAMjD,EAAE,GAAGkD,UAAU,CAAC,MAAMF,UAAU,CAACG,KAAK,EAAE,EAAEnH,OAAO,CAAC;IAExD,MAAM0F,QAAQ,GAAG,MAAM,IAAI,CAAC1G,MAAM,CAACoI,KAAK,CAACL,GAAG,EAAAvH,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACvCyF,OAAO;MACVmC,MAAM,EAAEL,UAAU,CAACK;IAAM,GACzB;IAEFR,YAAY,CAAC7C,EAAE,CAAC;IAEhB,OAAO0B,QAAQ;EACjB;EAEA;EACAO,KAAKA,CACHtC,KAAa,EACblC,OAA+B,EAC/BzB,OAAO,GAAG,IAAI,CAACA,OAAO;IAEtB,IAAI,CAAC,IAAI,CAACZ,UAAU,EAAE;MACpB,MAAM,kBAAkBuE,KAAK,SAAS,IAAI,CAAC9E,KAAK,iEAAiE;IACnH;IACA,IAAI6B,SAAS,GAAG,IAAIxC,IAAI,CAAC,IAAI,EAAEyF,KAAK,EAAElC,OAAO,EAAEzB,OAAO,CAAC;IACvD,IAAI,IAAI,CAAC+E,QAAQ,EAAE,EAAE;MACnBrE,SAAS,CAACC,IAAI,EAAE;IAClB,CAAC,MAAM;MACLD,SAAS,CAAC4G,YAAY,EAAE;MACxB,IAAI,CAACjI,UAAU,CAAC0E,IAAI,CAACrD,SAAS,CAAC;IACjC;IAEA,OAAOA,SAAS;EAClB;EAEA;;;;;;;;EAQA6G,UAAUA,CAACC,MAAc,EAAE/F,OAAY,EAAEgG,IAAa;IACpD,OAAOhG,OAAO;EAChB;EAEA;EACAiG,SAASA,CAAC7I,KAAa;IACrB,OAAO,IAAI,CAACA,KAAK,KAAKA,KAAK;EAC7B;EAEA;EACAiC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACb,QAAQ,CAACyB,GAAG;EAC1B;EAEA;EACAC,QAAQA,CAACgD,IAAY,EAAElD,OAAa,EAAEC,GAAY;;IAChD,MAAMiG,SAAS,GAAGhD,IAAI,CAACiD,iBAAiB,EAAE;IAC1C,MAAM;MAAEtB,KAAK;MAAEnC,KAAK;MAAEsC,KAAK;MAAEvG;IAAI,CAAE,GAAGlC,cAAc;IACpD,MAAM6J,MAAM,GAAa,CAACvB,KAAK,EAAEnC,KAAK,EAAEsC,KAAK,EAAEvG,IAAI,CAAC;IACpD,IAAIwB,GAAG,IAAImG,MAAM,CAACC,OAAO,CAACH,SAAS,CAAC,IAAI,CAAC,IAAIjG,GAAG,KAAK,IAAI,CAACZ,QAAQ,EAAE,EAAE;MACpE;IACF;IACA,IAAIiH,cAAc,GAAG,IAAI,CAACR,UAAU,CAACI,SAAS,EAAElG,OAAO,EAAEC,GAAG,CAAC;IAC7D,IAAID,OAAO,IAAI,CAACsG,cAAc,EAAE;MAC9B,MAAM,6EAA6E;IACrF;IAEA,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACL,SAAS,CAAC,EAAE;MACtD,CAAAjF,EAAA,OAAI,CAACzD,QAAQ,CAACuD,gBAAgB,cAAAE,EAAA,uBAAAA,EAAA,CAC1BG,MAAM,CAAEoF,IAAI,IAAI;;QAChB,OACE,EAAAvF,EAAA,GAAAuF,IAAI,CAACpF,MAAM,cAAAH,EAAA,uBAAAA,EAAA,CAAEiB,KAAK,MAAK,GAAG,IAC1B,EAAAuC,EAAA,IAAAzD,EAAA,GAAAwF,IAAI,CAACpF,MAAM,cAAAJ,EAAA,uBAAAA,EAAA,CAAEkB,KAAK,cAAAuC,EAAA,uBAAAA,EAAA,CAAE0B,iBAAiB,EAAE,MAAKD,SAAS;MAEzD,CAAC,EACAhF,GAAG,CAAEsF,IAAI,IAAKA,IAAI,CAACjG,QAAQ,CAAC+F,cAAc,EAAErG,GAAG,CAAC,CAAC;IACtD,CAAC,MAAM;MACL,CAAAe,EAAA,OAAI,CAACxD,QAAQ,CAAC0I,SAAS,CAAC,cAAAlF,EAAA,uBAAAA,EAAA,CACpBI,MAAM,CAAEoF,IAAI,IAAI;;QAChB,IACE,CAAC,WAAW,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAACD,QAAQ,CAACL,SAAS,CAAC,EACjE;UACA,IAAI,IAAI,IAAIM,IAAI,EAAE;YAChB,MAAMC,MAAM,GAAGD,IAAI,CAACjE,EAAE;YACtB,MAAMmE,SAAS,GAAG,CAAAzF,EAAA,GAAAuF,IAAI,CAACpF,MAAM,cAAAH,EAAA,uBAAAA,EAAA,CAAEiB,KAAK;YACpC,OACEuE,MAAM,KACN,CAAAzF,EAAA,GAAAhB,OAAO,CAAC2G,GAAG,cAAA3F,EAAA,uBAAAA,EAAA,CAAEuF,QAAQ,CAACE,MAAM,CAAC,MAC5BC,SAAS,KAAK,GAAG,IAChB,CAAAA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEP,iBAAiB,EAAE,OAC5B,CAAA1B,EAAA,GAAAzE,OAAO,CAAC4G,IAAI,cAAAnC,EAAA,uBAAAA,EAAA,CAAEvB,IAAI,CAACiD,iBAAiB,EAAE,EAAC;UAE/C,CAAC,MAAM;YACL,MAAMO,SAAS,GAAG,CAAAG,EAAA,IAAAC,EAAA,GAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEpF,MAAM,cAAA0F,EAAA,uBAAAA,EAAA,CAAE5E,KAAK,cAAA2E,EAAA,uBAAAA,EAAA,CAAEV,iBAAiB,EAAE;YAC1D,OACEO,SAAS,KAAK,GAAG,IACjBA,SAAS,MAAK,CAAAK,EAAA,GAAA/G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkC,KAAK,cAAA6E,EAAA,uBAAAA,EAAA,CAAEZ,iBAAiB,EAAE;UAErD;QACF,CAAC,MAAM;UACL,OAAOK,IAAI,CAACtD,IAAI,CAACiD,iBAAiB,EAAE,KAAKD,SAAS;QACpD;MACF,CAAC,EACAhF,GAAG,CAAEsF,IAAI,IAAI;QACZ,IAAI,OAAOF,cAAc,KAAK,QAAQ,IAAI,KAAK,IAAIA,cAAc,EAAE;UACjE,MAAMU,eAAe,GAAGV,cAAc,CAACM,IAAI;UAC3C,MAAM;YAAEzE,MAAM;YAAEC,KAAK;YAAE6E,gBAAgB;YAAE/D,IAAI;YAAEgE;UAAM,CAAE,GACrDF,eAAe;UACjB,MAAMG,eAAe,GAAG;YACtBhF,MAAM,EAAEA,MAAM;YACdC,KAAK,EAAEA,KAAK;YACZ6E,gBAAgB,EAAEA,gBAAgB;YAClCG,SAAS,EAAElE,IAAI;YACfmE,GAAG,EAAE,EAAE;YACPC,GAAG,EAAE,EAAE;YACPJ,MAAM,EAAEA;WACT;UACDZ,cAAc,GAAAvI,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACTmJ,eAAe,GACf,IAAI,CAACI,kBAAkB,CAACP,eAAe,CAAC,CAC5C;QACH;QACAR,IAAI,CAACjG,QAAQ,CAAC+F,cAAc,EAAErG,GAAG,CAAC;MACpC,CAAC,CAAC;IACN;EACF;EAEA;EACAP,SAASA,CAAA;IACP,OAAO,IAAI,CAACjC,KAAK,KAAKjB,cAAc,CAACkB,MAAM;EAC7C;EAEA;EACA8J,SAASA,CAAA;IACP,OAAO,IAAI,CAAC/J,KAAK,KAAKjB,cAAc,CAACsC,MAAM;EAC7C;EAEA;EACAe,UAAUA,CAAA;IACR,OAAO,IAAI,CAACpC,KAAK,KAAKjB,cAAc,CAACiL,OAAO;EAC9C;EAEA;EACAhI,UAAUA,CAAA;IACR,OAAO,IAAI,CAAChC,KAAK,KAAKjB,cAAc,CAACmI,OAAO;EAC9C;EAEA;EACAxE,eAAeA,CAACF,GAAW;IACzB,OAAO,cAAcA,GAAG,EAAE;EAC5B;EAEA;EACAH,GAAGA,CAACoD,IAAY,EAAE9B,MAA8B,EAAEb,QAAkB;IAClE,MAAM2F,SAAS,GAAGhD,IAAI,CAACiD,iBAAiB,EAAE;IAE1C,MAAMuB,OAAO,GAAG;MACdxE,IAAI,EAAEgD,SAAS;MACf9E,MAAM,EAAEA,MAAM;MACdb,QAAQ,EAAEA;KACX;IAED,IAAI,IAAI,CAAC/C,QAAQ,CAAC0I,SAAS,CAAC,EAAE;MAC5B,IAAI,CAAC1I,QAAQ,CAAC0I,SAAS,CAAC,CAAC5D,IAAI,CAACoF,OAAO,CAAC;IACxC,CAAC,MAAM;MACL,IAAI,CAAClK,QAAQ,CAAC0I,SAAS,CAAC,GAAG,CAACwB,OAAO,CAAC;IACtC;IAEA,OAAO,IAAI;EACb;EAEA;EACAC,IAAIA,CAACzE,IAAY,EAAE9B,MAA8B;IAC/C,MAAM8E,SAAS,GAAGhD,IAAI,CAACiD,iBAAiB,EAAE;IAE1C,IAAI,CAAC3I,QAAQ,CAAC0I,SAAS,CAAC,GAAG,IAAI,CAAC1I,QAAQ,CAAC0I,SAAS,CAAC,CAAC9E,MAAM,CAAEoF,IAAI,IAAI;;MAClE,OAAO,EACL,EAAAvF,EAAA,GAAAuF,IAAI,CAACtD,IAAI,cAAAjC,EAAA,uBAAAA,EAAA,CAAEkF,iBAAiB,EAAE,MAAKD,SAAS,IAC5ChJ,eAAe,CAAC0K,OAAO,CAACpB,IAAI,CAACpF,MAAM,EAAEA,MAAM,CAAC,CAC7C;IACH,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEA;EACQ,OAAOwG,OAAOA,CACpBC,IAA+B,EAC/BC,IAA+B;IAE/B,IAAI/J,MAAM,CAACgK,IAAI,CAACF,IAAI,CAAC,CAAC/F,MAAM,KAAK/D,MAAM,CAACgK,IAAI,CAACD,IAAI,CAAC,CAAChG,MAAM,EAAE;MACzD,OAAO,KAAK;IACd;IAEA,KAAK,MAAMkG,CAAC,IAAIH,IAAI,EAAE;MACpB,IAAIA,IAAI,CAACG,CAAC,CAAC,KAAKF,IAAI,CAACE,CAAC,CAAC,EAAE;QACvB,OAAO,KAAK;MACd;IACF;IAEA,OAAO,IAAI;EACb;EAEA;EACQrJ,qBAAqBA,CAAA;IAC3B,IAAI,CAACD,WAAW,CAACkB,eAAe,EAAE;IAClC,IAAI,IAAI,CAACrC,MAAM,CAACiD,WAAW,EAAE,EAAE;MAC7B,IAAI,CAACgB,OAAO,EAAE;IAChB;EACF;EAEA;;;;;EAKQrC,QAAQA,CAACoB,QAAkB;IACjC,IAAI,CAACT,GAAG,CAACvD,cAAc,CAACsI,KAAK,EAAE,EAAE,EAAEtE,QAAQ,CAAC;EAC9C;EAEA;;;;;EAKQhB,QAAQA,CAACgB,QAAkB;IACjC,IAAI,CAACT,GAAG,CAACvD,cAAc,CAACmG,KAAK,EAAE,EAAE,EAAGlD,MAAc,IAAKe,QAAQ,CAACf,MAAM,CAAC,CAAC;EAC1E;EAEA;;;;;EAKQ8D,QAAQA,CAAA;IACd,OAAO,IAAI,CAAC/F,MAAM,CAACiD,WAAW,EAAE,IAAI,IAAI,CAACgH,SAAS,EAAE;EACtD;EAEA;EACQhG,OAAOA,CAACjD,OAAO,GAAG,IAAI,CAACA,OAAO;IACpC,IAAI,IAAI,CAACkB,UAAU,EAAE,EAAE;MACrB;IACF;IACA,IAAI,CAAClC,MAAM,CAAC0K,eAAe,CAAC,IAAI,CAAC7K,KAAK,CAAC;IACvC,IAAI,CAACK,KAAK,GAAGjB,cAAc,CAACiL,OAAO;IACnC,IAAI,CAACjJ,QAAQ,CAAC0J,MAAM,CAAC3J,OAAO,CAAC;EAC/B;EAEA;EACQgJ,kBAAkBA,CAACvH,OAAY;IACrC,MAAMmI,OAAO,GAAG;MACdd,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE;KACN;IAED,IAAItH,OAAO,CAACkD,IAAI,KAAK,QAAQ,IAAIlD,OAAO,CAACkD,IAAI,KAAK,QAAQ,EAAE;MAC1DiF,OAAO,CAACd,GAAG,GAAGzK,YAAY,CAACwL,iBAAiB,CAC1CpI,OAAO,CAACqI,OAAO,EACfrI,OAAO,CAACsI,MAAM,CACf;IACH;IAEA,IAAItI,OAAO,CAACkD,IAAI,KAAK,QAAQ,IAAIlD,OAAO,CAACkD,IAAI,KAAK,QAAQ,EAAE;MAC1DiF,OAAO,CAACb,GAAG,GAAG1K,YAAY,CAACwL,iBAAiB,CAC1CpI,OAAO,CAACqI,OAAO,EACfrI,OAAO,CAACuI,UAAU,CACnB;IACH;IAEA,OAAOJ,OAAO;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}