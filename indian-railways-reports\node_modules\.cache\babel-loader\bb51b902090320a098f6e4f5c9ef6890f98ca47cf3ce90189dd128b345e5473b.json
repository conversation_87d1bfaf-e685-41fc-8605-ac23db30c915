{"ast": null, "code": "import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n  constructor(url, headers = {}, fetch, opts) {\n    super(url, headers, fetch, opts);\n  }\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id) {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch);\n  }\n}", "map": {"version": 3, "names": ["StorageFileApi", "StorageBucketApi", "StorageClient", "constructor", "url", "headers", "fetch", "opts", "from", "id"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\storage-js\\src\\StorageClient.ts"], "sourcesContent": ["import StorageFileApi from './packages/StorageFileApi'\nimport StorageBucketApi from './packages/StorageBucketApi'\nimport { Fetch } from './lib/fetch'\n\nexport interface StorageClientOptions {\n  useNewHostname?: boolean\n}\n\nexport class StorageClient extends StorageBucketApi {\n  constructor(\n    url: string,\n    headers: { [key: string]: string } = {},\n    fetch?: Fetch,\n    opts?: StorageClientOptions\n  ) {\n    super(url, headers, fetch, opts)\n  }\n\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id: string): StorageFileApi {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch)\n  }\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,6BAA6B;AAO1D,OAAM,MAAOC,aAAc,SAAQD,gBAAgB;EACjDE,YACEC,GAAW,EACXC,OAAA,GAAqC,EAAE,EACvCC,KAAa,EACbC,IAA2B;IAE3B,KAAK,CAACH,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,CAAC;EAClC;EAEA;;;;;EAKAC,IAAIA,CAACC,EAAU;IACb,OAAO,IAAIT,cAAc,CAAC,IAAI,CAACI,GAAG,EAAE,IAAI,CAACC,OAAO,EAAEI,EAAE,EAAE,IAAI,CAACH,KAAK,CAAC;EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}