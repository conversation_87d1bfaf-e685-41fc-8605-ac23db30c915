{"ast": null, "code": "/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}", "map": {"version": 3, "names": ["memoryLocalStorageAdapter", "store", "getItem", "key", "setItem", "value", "removeItem"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\auth-js\\src\\lib\\local-storage.ts"], "sourcesContent": ["import { SupportedStorage } from './types'\n\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store: { [key: string]: string } = {}): SupportedStorage {\n  return {\n    getItem: (key) => {\n      return store[key] || null\n    },\n\n    setItem: (key, value) => {\n      store[key] = value\n    },\n\n    removeItem: (key) => {\n      delete store[key]\n    },\n  }\n}\n"], "mappings": "AAEA;;;;AAIA,OAAM,SAAUA,yBAAyBA,CAACC,KAAA,GAAmC,EAAE;EAC7E,OAAO;IACLC,OAAO,EAAGC,GAAG,IAAI;MACf,OAAOF,KAAK,CAACE,GAAG,CAAC,IAAI,IAAI;IAC3B,CAAC;IAEDC,OAAO,EAAEA,CAACD,GAAG,EAAEE,KAAK,KAAI;MACtBJ,KAAK,CAACE,GAAG,CAAC,GAAGE,KAAK;IACpB,CAAC;IAEDC,UAAU,EAAGH,GAAG,IAAI;MAClB,OAAOF,KAAK,CAACE,GAAG,CAAC;IACnB;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}