{"ast": null, "code": "import { getNativeWebSocket } from \"./utils.js\";\nexport const WebSocket = getNativeWebSocket();", "map": {"version": 3, "names": ["getNativeWebSocket", "WebSocket"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\isows\\native.ts"], "sourcesContent": ["import { getNativeWebSocket } from \"./utils.js\";\n\nexport const WebSocket = getNativeWebSocket();\n\ntype MessageEvent_ = MessageEvent;\nexport type { MessageEvent_ as MessageEvent };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,YAAY;AAE/C,OAAO,MAAMC,SAAS,GAAGD,kBAAkB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}