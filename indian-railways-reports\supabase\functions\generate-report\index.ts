import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { createReport } from 'https://esm.sh/docx-templates@4.11.5'
import { PDFDocument, rgb, StandardFonts } from 'https://esm.sh/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    if (req.method === 'POST') {
      const url = new URL(req.url)
      const reportId = url.pathname.split('/').pop()

      if (!reportId) {
        return new Response(
          JSON.stringify({ error: 'Report ID required' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400 
          }
        )
      }

      // Get report and template data
      const { data: report, error: reportError } = await supabaseClient
        .from('reports')
        .select(`
          *,
          templates (
            file_url,
            placeholder_map
          )
        `)
        .eq('id', reportId)
        .single()

      if (reportError) throw reportError

      // Download template file
      const templateResponse = await fetch(report.templates.file_url)
      const templateBuffer = await templateResponse.arrayBuffer()

      // Generate document (simplified - would use docx-templates in production)
      const generatedDoc = await generateDocument(templateBuffer, report.data, report.templates.placeholder_map)

      // Upload generated document
      const docFileName = `generated-${reportId}-${Date.now()}.docx`
      const { data: docUpload, error: docUploadError } = await supabaseClient.storage
        .from('generated-reports')
        .upload(docFileName, generatedDoc, {
          contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

      if (docUploadError) throw docUploadError

      // Get public URL for document
      const { data: { publicUrl: docUrl } } = supabaseClient.storage
        .from('generated-reports')
        .getPublicUrl(docFileName)

      // Generate PDF with report data
      const pdfBuffer = await convertToPDF(generatedDoc, report.data)
      
      // Upload PDF
      const pdfFileName = `generated-${reportId}-${Date.now()}.pdf`
      const { data: pdfUpload, error: pdfUploadError } = await supabaseClient.storage
        .from('generated-reports')
        .upload(pdfFileName, pdfBuffer, {
          contentType: 'application/pdf'
        })

      if (pdfUploadError) throw pdfUploadError

      // Get public URL for PDF
      const { data: { publicUrl: pdfUrl } } = supabaseClient.storage
        .from('generated-reports')
        .getPublicUrl(pdfFileName)

      // Update report with generated file URLs
      const { data: updatedReport, error: updateError } = await supabaseClient
        .from('reports')
        .update({
          generated_doc_url: docUrl,
          generated_pdf_url: pdfUrl,
          status: 'generated'
        })
        .eq('id', reportId)
        .select()
        .single()

      if (updateError) throw updateError

      return new Response(
        JSON.stringify(updatedReport),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 405 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

// Document generation using docx-templates
async function generateDocument(templateBuffer: ArrayBuffer, data: Record<string, any>, placeholderMap: Record<string, string>): Promise<Uint8Array> {
  try {
    console.log('Generating document with data:', data);

    // Prepare data for template processing
    const templateData = {
      ...data,
      // Add current date if not provided
      Date: data.Date || new Date().toLocaleDateString('en-IN'),
      // Add current time if not provided
      Time: data.Time || new Date().toLocaleTimeString('en-IN', { hour12: false }),
    };

    // Generate document using docx-templates
    const report = await createReport({
      template: templateBuffer,
      data: templateData,
      cmdDelimiter: ['{{', '}}'], // Use {{ }} delimiters for placeholders
      failFast: false, // Continue processing even if some placeholders are missing
      noSandBox: false, // Enable sandbox for security
    });

    console.log('Document generated successfully');
    return new Uint8Array(report);
  } catch (error) {
    console.error('Document generation error:', error);
    throw new Error(`Failed to generate document: ${error.message}`);
  }
}

// PDF conversion using pdf-lib
async function convertToPDF(docxBuffer: Uint8Array, reportData?: Record<string, any>): Promise<Uint8Array> {
  try {
    console.log('Converting to PDF...');

    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();

    // Add a page
    const page = pdfDoc.addPage([595.28, 841.89]); // A4 size in points
    const { width, height } = page.getSize();

    // Get fonts
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Add header
    page.drawText('INDIAN RAILWAYS', {
      x: 50,
      y: height - 50,
      size: 20,
      font: boldFont,
      color: rgb(0, 0.2, 0.4), // Railway blue
    });

    page.drawText('OFFICIAL REPORT', {
      x: 50,
      y: height - 80,
      size: 16,
      font: boldFont,
      color: rgb(1, 0.4, 0), // Railway orange
    });

    // Add a line separator
    page.drawLine({
      start: { x: 50, y: height - 100 },
      end: { x: width - 50, y: height - 100 },
      thickness: 2,
      color: rgb(0, 0.2, 0.4),
    });

    // Add report content
    let yPosition = height - 140;
    const lineHeight = 20;

    if (reportData) {
      // Add report data
      Object.entries(reportData).forEach(([key, value]) => {
        if (key !== 'Signature' && value) {
          page.drawText(`${key}: ${value}`, {
            x: 50,
            y: yPosition,
            size: 12,
            font: font,
            color: rgb(0, 0, 0),
          });
          yPosition -= lineHeight;
        }
      });
    } else {
      // Default content if no data provided
      page.drawText('Report generated from DOCX template', {
        x: 50,
        y: yPosition,
        size: 12,
        font: font,
        color: rgb(0, 0, 0),
      });
      yPosition -= lineHeight;

      page.drawText(`Generated on: ${new Date().toLocaleString('en-IN')}`, {
        x: 50,
        y: yPosition,
        size: 12,
        font: font,
        color: rgb(0.5, 0.5, 0.5),
      });
    }

    // Add footer
    page.drawText('This is an official document generated by Indian Railways Report System', {
      x: 50,
      y: 50,
      size: 10,
      font: font,
      color: rgb(0.5, 0.5, 0.5),
    });

    // Serialize the PDF
    const pdfBytes = await pdfDoc.save();
    console.log('PDF generated successfully');

    return new Uint8Array(pdfBytes);
  } catch (error) {
    console.error('PDF conversion error:', error);
    throw new Error(`Failed to convert to PDF: ${error.message}`);
  }
}
