{"ast": null, "code": "var _jsxFileName = \"C:\\\\Doc Maker AI\\\\indian-railways-reports\\\\src\\\\components\\\\TemplateManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemplateManagement = () => {\n  _s();\n  const [templates, setTemplates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState(null);\n  const [showUploadForm, setShowUploadForm] = useState(false);\n\n  // Form state\n  const [templateName, setTemplateName] = useState('');\n  const [templateType, setTemplateType] = useState('joint_report');\n  const [selectedFile, setSelectedFile] = useState(null);\n  useEffect(() => {\n    fetchTemplates();\n  }, []);\n  const fetchTemplates = async () => {\n    try {\n      setLoading(true);\n      const {\n        data,\n        error\n      } = await supabase.from('templates').select('*').order('created_at', {\n        ascending: false\n      });\n      if (error) throw error;\n      setTemplates(data || []);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFileChange = e => {\n    var _e$target$files;\n    const file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n    if (file && file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n      setSelectedFile(file);\n      setError(null);\n    } else {\n      setError('Please select a valid .docx file');\n      setSelectedFile(null);\n    }\n  };\n  const handleUpload = async e => {\n    e.preventDefault();\n    if (!selectedFile || !templateName.trim()) {\n      setError('Please provide a template name and select a file');\n      return;\n    }\n    try {\n      setUploading(true);\n      setError(null);\n\n      // Upload file to Supabase Storage\n      const fileName = `${Date.now()}-${selectedFile.name}`;\n      const {\n        data: uploadData,\n        error: uploadError\n      } = await supabase.storage.from('templates').upload(fileName, selectedFile);\n      if (uploadError) throw uploadError;\n\n      // Get public URL\n      const {\n        data: {\n          publicUrl\n        }\n      } = supabase.storage.from('templates').getPublicUrl(fileName);\n\n      // Extract placeholders (simplified)\n      const placeholderMap = await extractPlaceholders(selectedFile);\n\n      // Save template metadata\n      const {\n        data: template,\n        error: dbError\n      } = await supabase.from('templates').insert({\n        name: templateName,\n        type: templateType,\n        file_url: publicUrl,\n        placeholder_map: placeholderMap\n      }).select().single();\n      if (dbError) throw dbError;\n\n      // Reset form\n      setTemplateName('');\n      setTemplateType('joint_report');\n      setSelectedFile(null);\n      setShowUploadForm(false);\n\n      // Refresh templates list\n      fetchTemplates();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Upload failed');\n    } finally {\n      setUploading(false);\n    }\n  };\n  const extractPlaceholders = async file => {\n    // Simplified placeholder extraction\n    // In production, you'd use a proper docx parsing library\n    const placeholders = {};\n\n    // Common placeholders for Indian Railways reports\n    const commonPlaceholders = {\n      'Date': 'Date',\n      'Loco_No': 'Locomotive Number',\n      'Train_No': 'Train Number',\n      'Station': 'Station',\n      'Time': 'Time',\n      'Officer_Name': 'Officer Name',\n      'Designation': 'Designation',\n      'Signature': 'Digital Signature'\n    };\n    return commonPlaceholders;\n  };\n  const deleteTemplate = async (id, fileUrl) => {\n    if (!window.confirm('Are you sure you want to delete this template?')) {\n      return;\n    }\n    try {\n      // Delete from database\n      const {\n        error: dbError\n      } = await supabase.from('templates').delete().eq('id', id);\n      if (dbError) throw dbError;\n\n      // Delete file from storage\n      const fileName = fileUrl.split('/').pop();\n      if (fileName) {\n        await supabase.storage.from('templates').remove([fileName]);\n      }\n\n      // Refresh templates list\n      fetchTemplates();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Delete failed');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-railway-blue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: \"Template Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowUploadForm(!showUploadForm),\n        className: \"bg-railway-blue hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors duration-200\",\n        children: showUploadForm ? 'Cancel' : 'Upload Template'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this), showUploadForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold mb-4\",\n        children: \"Upload New Template\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleUpload,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Template Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: templateName,\n            onChange: e => setTemplateName(e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\",\n            placeholder: \"Enter template name\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Template Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: templateType,\n            onChange: e => setTemplateType(e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"joint_report\",\n              children: \"Joint Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ta_form\",\n              children: \"TA Form\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Template File (.docx)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            accept: \".docx\",\n            onChange: handleFileChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: uploading,\n          className: \"bg-railway-orange hover:bg-orange-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200\",\n          children: uploading ? 'Uploading...' : 'Upload Template'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg rounded-lg overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Templates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), templates.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No templates found. Upload your first template to get started.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Created\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: templates.map(template => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: template.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500\",\n                  children: template.type.replace('_', ' ').toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: new Date(template.created_at).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                  href: template.file_url,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"text-railway-blue hover:text-blue-800 mr-4\",\n                  children: \"Download\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => deleteTemplate(template.id, template.file_url),\n                  className: \"text-red-600 hover:text-red-800\",\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this)]\n            }, template.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(TemplateManagement, \"SWM9HeMig65baEyEbjdO2h1xtG8=\");\n_c = TemplateManagement;\nexport default TemplateManagement;\nvar _c;\n$RefreshReg$(_c, \"TemplateManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "supabase", "jsxDEV", "_jsxDEV", "TemplateManagement", "_s", "templates", "setTemplates", "loading", "setLoading", "uploading", "setUploading", "error", "setError", "showUploadForm", "setShowUploadForm", "templateName", "setTemplateName", "templateType", "setTemplateType", "selectedFile", "setSelectedFile", "fetchTemplates", "data", "from", "select", "order", "ascending", "err", "Error", "message", "handleFileChange", "e", "_e$target$files", "file", "target", "files", "type", "handleUpload", "preventDefault", "trim", "fileName", "Date", "now", "name", "uploadData", "uploadError", "storage", "upload", "publicUrl", "getPublicUrl", "placeholderM<PERSON>", "extractPlaceholders", "template", "db<PERSON><PERSON>r", "insert", "file_url", "placeholder_map", "single", "placeholders", "commonPlaceholders", "deleteTemplate", "id", "fileUrl", "window", "confirm", "delete", "eq", "split", "pop", "remove", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "value", "onChange", "placeholder", "required", "accept", "disabled", "length", "map", "replace", "toUpperCase", "created_at", "toLocaleDateString", "href", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Doc Maker AI/indian-railways-reports/src/components/TemplateManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { supabase, Template } from '../lib/supabase';\n\nconst TemplateManagement: React.FC = () => {\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [showUploadForm, setShowUploadForm] = useState(false);\n\n  // Form state\n  const [templateName, setTemplateName] = useState('');\n  const [templateType, setTemplateType] = useState<'joint_report' | 'ta_form'>('joint_report');\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  useEffect(() => {\n    fetchTemplates();\n  }, []);\n\n  const fetchTemplates = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await supabase\n        .from('templates')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      setTemplates(data || []);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file && file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {\n      setSelectedFile(file);\n      setError(null);\n    } else {\n      setError('Please select a valid .docx file');\n      setSelectedFile(null);\n    }\n  };\n\n  const handleUpload = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!selectedFile || !templateName.trim()) {\n      setError('Please provide a template name and select a file');\n      return;\n    }\n\n    try {\n      setUploading(true);\n      setError(null);\n\n      // Upload file to Supabase Storage\n      const fileName = `${Date.now()}-${selectedFile.name}`;\n      const { data: uploadData, error: uploadError } = await supabase.storage\n        .from('templates')\n        .upload(fileName, selectedFile);\n\n      if (uploadError) throw uploadError;\n\n      // Get public URL\n      const { data: { publicUrl } } = supabase.storage\n        .from('templates')\n        .getPublicUrl(fileName);\n\n      // Extract placeholders (simplified)\n      const placeholderMap = await extractPlaceholders(selectedFile);\n\n      // Save template metadata\n      const { data: template, error: dbError } = await supabase\n        .from('templates')\n        .insert({\n          name: templateName,\n          type: templateType,\n          file_url: publicUrl,\n          placeholder_map: placeholderMap\n        })\n        .select()\n        .single();\n\n      if (dbError) throw dbError;\n\n      // Reset form\n      setTemplateName('');\n      setTemplateType('joint_report');\n      setSelectedFile(null);\n      setShowUploadForm(false);\n      \n      // Refresh templates list\n      fetchTemplates();\n      \n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Upload failed');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const extractPlaceholders = async (file: File): Promise<Record<string, string>> => {\n    // Simplified placeholder extraction\n    // In production, you'd use a proper docx parsing library\n    const placeholders: Record<string, string> = {};\n    \n    // Common placeholders for Indian Railways reports\n    const commonPlaceholders = {\n      'Date': 'Date',\n      'Loco_No': 'Locomotive Number',\n      'Train_No': 'Train Number',\n      'Station': 'Station',\n      'Time': 'Time',\n      'Officer_Name': 'Officer Name',\n      'Designation': 'Designation',\n      'Signature': 'Digital Signature'\n    };\n    \n    return commonPlaceholders;\n  };\n\n  const deleteTemplate = async (id: string, fileUrl: string) => {\n    if (!window.confirm('Are you sure you want to delete this template?')) {\n      return;\n    }\n\n    try {\n      // Delete from database\n      const { error: dbError } = await supabase\n        .from('templates')\n        .delete()\n        .eq('id', id);\n\n      if (dbError) throw dbError;\n\n      // Delete file from storage\n      const fileName = fileUrl.split('/').pop();\n      if (fileName) {\n        await supabase.storage\n          .from('templates')\n          .remove([fileName]);\n      }\n\n      // Refresh templates list\n      fetchTemplates();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Delete failed');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-railway-blue\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">Template Management</h1>\n        <button\n          onClick={() => setShowUploadForm(!showUploadForm)}\n          className=\"bg-railway-blue hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors duration-200\"\n        >\n          {showUploadForm ? 'Cancel' : 'Upload Template'}\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n          {error}\n        </div>\n      )}\n\n      {showUploadForm && (\n        <div className=\"bg-white shadow-lg rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">Upload New Template</h2>\n          <form onSubmit={handleUpload} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Template Name\n              </label>\n              <input\n                type=\"text\"\n                value={templateName}\n                onChange={(e) => setTemplateName(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\"\n                placeholder=\"Enter template name\"\n                required\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Template Type\n              </label>\n              <select\n                value={templateType}\n                onChange={(e) => setTemplateType(e.target.value as 'joint_report' | 'ta_form')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\"\n              >\n                <option value=\"joint_report\">Joint Report</option>\n                <option value=\"ta_form\">TA Form</option>\n              </select>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Template File (.docx)\n              </label>\n              <input\n                type=\"file\"\n                accept=\".docx\"\n                onChange={handleFileChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue\"\n                required\n              />\n            </div>\n            \n            <button\n              type=\"submit\"\n              disabled={uploading}\n              className=\"bg-railway-orange hover:bg-orange-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200\"\n            >\n              {uploading ? 'Uploading...' : 'Upload Template'}\n            </button>\n          </form>\n        </div>\n      )}\n\n      <div className=\"bg-white shadow-lg rounded-lg overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Templates</h2>\n        </div>\n        \n        {templates.length === 0 ? (\n          <div className=\"px-6 py-8 text-center\">\n            <p className=\"text-gray-500\">No templates found. Upload your first template to get started.</p>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Name\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Type\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Created\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {templates.map((template) => (\n                  <tr key={template.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {template.name}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-500\">\n                        {template.type.replace('_', ' ').toUpperCase()}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {new Date(template.created_at).toLocaleDateString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <a\n                        href={template.file_url}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-railway-blue hover:text-blue-800 mr-4\"\n                      >\n                        Download\n                      </a>\n                      <button\n                        onClick={() => deleteTemplate(template.id, template.file_url)}\n                        className=\"text-red-600 hover:text-red-800\"\n                      >\n                        Delete\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TemplateManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAkB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAA6B,cAAc,CAAC;EAC5F,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAc,IAAI,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACdsB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM;QAAEc,IAAI;QAAEX;MAAM,CAAC,GAAG,MAAMX,QAAQ,CACnCuB,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;MAE5C,IAAIf,KAAK,EAAE,MAAMA,KAAK;MACtBL,YAAY,CAACgB,IAAI,IAAI,EAAE,CAAC;IAC1B,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZf,QAAQ,CAACe,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,mBAAmB,CAAC;IACpE,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAIC,CAAsC,IAAK;IAAA,IAAAC,eAAA;IACnE,MAAMC,IAAI,IAAAD,eAAA,GAAGD,CAAC,CAACG,MAAM,CAACC,KAAK,cAAAH,eAAA,uBAAdA,eAAA,CAAiB,CAAC,CAAC;IAChC,IAAIC,IAAI,IAAIA,IAAI,CAACG,IAAI,KAAK,yEAAyE,EAAE;MACnGhB,eAAe,CAACa,IAAI,CAAC;MACrBrB,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,MAAM;MACLA,QAAQ,CAAC,kCAAkC,CAAC;MAC5CQ,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAON,CAAkB,IAAK;IACjDA,CAAC,CAACO,cAAc,CAAC,CAAC;IAElB,IAAI,CAACnB,YAAY,IAAI,CAACJ,YAAY,CAACwB,IAAI,CAAC,CAAC,EAAE;MACzC3B,QAAQ,CAAC,kDAAkD,CAAC;MAC5D;IACF;IAEA,IAAI;MACFF,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM4B,QAAQ,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIvB,YAAY,CAACwB,IAAI,EAAE;MACrD,MAAM;QAAErB,IAAI,EAAEsB,UAAU;QAAEjC,KAAK,EAAEkC;MAAY,CAAC,GAAG,MAAM7C,QAAQ,CAAC8C,OAAO,CACpEvB,IAAI,CAAC,WAAW,CAAC,CACjBwB,MAAM,CAACP,QAAQ,EAAErB,YAAY,CAAC;MAEjC,IAAI0B,WAAW,EAAE,MAAMA,WAAW;;MAElC;MACA,MAAM;QAAEvB,IAAI,EAAE;UAAE0B;QAAU;MAAE,CAAC,GAAGhD,QAAQ,CAAC8C,OAAO,CAC7CvB,IAAI,CAAC,WAAW,CAAC,CACjB0B,YAAY,CAACT,QAAQ,CAAC;;MAEzB;MACA,MAAMU,cAAc,GAAG,MAAMC,mBAAmB,CAAChC,YAAY,CAAC;;MAE9D;MACA,MAAM;QAAEG,IAAI,EAAE8B,QAAQ;QAAEzC,KAAK,EAAE0C;MAAQ,CAAC,GAAG,MAAMrD,QAAQ,CACtDuB,IAAI,CAAC,WAAW,CAAC,CACjB+B,MAAM,CAAC;QACNX,IAAI,EAAE5B,YAAY;QAClBqB,IAAI,EAAEnB,YAAY;QAClBsC,QAAQ,EAAEP,SAAS;QACnBQ,eAAe,EAAEN;MACnB,CAAC,CAAC,CACD1B,MAAM,CAAC,CAAC,CACRiC,MAAM,CAAC,CAAC;MAEX,IAAIJ,OAAO,EAAE,MAAMA,OAAO;;MAE1B;MACArC,eAAe,CAAC,EAAE,CAAC;MACnBE,eAAe,CAAC,cAAc,CAAC;MAC/BE,eAAe,CAAC,IAAI,CAAC;MACrBN,iBAAiB,CAAC,KAAK,CAAC;;MAExB;MACAO,cAAc,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZf,QAAQ,CAACe,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,eAAe,CAAC;IAChE,CAAC,SAAS;MACRnB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMyC,mBAAmB,GAAG,MAAOlB,IAAU,IAAsC;IACjF;IACA;IACA,MAAMyB,YAAoC,GAAG,CAAC,CAAC;;IAE/C;IACA,MAAMC,kBAAkB,GAAG;MACzB,MAAM,EAAE,MAAM;MACd,SAAS,EAAE,mBAAmB;MAC9B,UAAU,EAAE,cAAc;MAC1B,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE,MAAM;MACd,cAAc,EAAE,cAAc;MAC9B,aAAa,EAAE,aAAa;MAC5B,WAAW,EAAE;IACf,CAAC;IAED,OAAOA,kBAAkB;EAC3B,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAOC,EAAU,EAAEC,OAAe,KAAK;IAC5D,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF;MACA,MAAM;QAAErD,KAAK,EAAE0C;MAAQ,CAAC,GAAG,MAAMrD,QAAQ,CACtCuB,IAAI,CAAC,WAAW,CAAC,CACjB0C,MAAM,CAAC,CAAC,CACRC,EAAE,CAAC,IAAI,EAAEL,EAAE,CAAC;MAEf,IAAIR,OAAO,EAAE,MAAMA,OAAO;;MAE1B;MACA,MAAMb,QAAQ,GAAGsB,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACzC,IAAI5B,QAAQ,EAAE;QACZ,MAAMxC,QAAQ,CAAC8C,OAAO,CACnBvB,IAAI,CAAC,WAAW,CAAC,CACjB8C,MAAM,CAAC,CAAC7B,QAAQ,CAAC,CAAC;MACvB;;MAEA;MACAnB,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZf,QAAQ,CAACe,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACE,OAAO,GAAG,eAAe,CAAC;IAChE;EACF,CAAC;EAED,IAAItB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKoE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDrE,OAAA;QAAKoE,SAAS,EAAC;MAAoE;QAAA9B,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC;EAEV;EAEA,oBACExE,OAAA;IAAKoE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBrE,OAAA;MAAKoE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrE,OAAA;QAAIoE,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAmB;QAAA/B,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzExE,OAAA;QACEyE,OAAO,EAAEA,CAAA,KAAM7D,iBAAiB,CAAC,CAACD,cAAc,CAAE;QAClDyD,SAAS,EAAC,kGAAkG;QAAAC,QAAA,EAE3G1D,cAAc,GAAG,QAAQ,GAAG;MAAiB;QAAA2B,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL/D,KAAK,iBACJT,OAAA;MAAKoE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,EAC7E5D;IAAK;MAAA6B,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA7D,cAAc,iBACbX,OAAA;MAAKoE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrE,OAAA;QAAIoE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAmB;QAAA/B,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnExE,OAAA;QAAM0E,QAAQ,EAAEvC,YAAa;QAACiC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDrE,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAOoE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAA/B,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxE,OAAA;YACEkC,IAAI,EAAC,MAAM;YACXyC,KAAK,EAAE9D,YAAa;YACpB+D,QAAQ,EAAG/C,CAAC,IAAKf,eAAe,CAACe,CAAC,CAACG,MAAM,CAAC2C,KAAK,CAAE;YACjDP,SAAS,EAAC,4GAA4G;YACtHS,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;UAAA;YAAAxC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAlC,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxE,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAOoE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAA/B,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxE,OAAA;YACE2E,KAAK,EAAE5D,YAAa;YACpB6D,QAAQ,EAAG/C,CAAC,IAAKb,eAAe,CAACa,CAAC,CAACG,MAAM,CAAC2C,KAAmC,CAAE;YAC/EP,SAAS,EAAC,4GAA4G;YAAAC,QAAA,gBAEtHrE,OAAA;cAAQ2E,KAAK,EAAC,cAAc;cAAAN,QAAA,EAAC;YAAY;cAAA/B,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClDxE,OAAA;cAAQ2E,KAAK,EAAC,SAAS;cAAAN,QAAA,EAAC;YAAO;cAAA/B,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAlC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAlC,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENxE,OAAA;UAAAqE,QAAA,gBACErE,OAAA;YAAOoE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAA/B,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxE,OAAA;YACEkC,IAAI,EAAC,MAAM;YACX6C,MAAM,EAAC,OAAO;YACdH,QAAQ,EAAEhD,gBAAiB;YAC3BwC,SAAS,EAAC,4GAA4G;YACtHU,QAAQ;UAAA;YAAAxC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAlC,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxE,OAAA;UACEkC,IAAI,EAAC,QAAQ;UACb8C,QAAQ,EAAEzE,SAAU;UACpB6D,SAAS,EAAC,2HAA2H;UAAAC,QAAA,EAEpI9D,SAAS,GAAG,cAAc,GAAG;QAAiB;UAAA+B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAEDxE,OAAA;MAAKoE,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5DrE,OAAA;QAAKoE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDrE,OAAA;UAAIoE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,EAELrE,SAAS,CAAC8E,MAAM,KAAK,CAAC,gBACrBjF,OAAA;QAAKoE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCrE,OAAA;UAAGoE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8D;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,gBAENxE,OAAA;QAAKoE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BrE,OAAA;UAAOoE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDrE,OAAA;YAAOoE,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BrE,OAAA;cAAAqE,QAAA,gBACErE,OAAA;gBAAIoE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxE,OAAA;gBAAIoE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxE,OAAA;gBAAIoE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxE,OAAA;gBAAIoE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAlC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRxE,OAAA;YAAOoE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDlE,SAAS,CAAC+E,GAAG,CAAEhC,QAAQ,iBACtBlD,OAAA;cAAsBoE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAChDrE,OAAA;gBAAIoE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCrE,OAAA;kBAAKoE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC/CnB,QAAQ,CAACT;gBAAI;kBAAAH,QAAA,EAAAgC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAlC,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxE,OAAA;gBAAIoE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCrE,OAAA;kBAAKoE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnCnB,QAAQ,CAAChB,IAAI,CAACiD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAA9C,QAAA,EAAAgC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAlC,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLxE,OAAA;gBAAIoE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D,IAAI9B,IAAI,CAACW,QAAQ,CAACmC,UAAU,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAhD,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACLxE,OAAA;gBAAIoE,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7DrE,OAAA;kBACEuF,IAAI,EAAErC,QAAQ,CAACG,QAAS;kBACxBrB,MAAM,EAAC,QAAQ;kBACfwD,GAAG,EAAC,qBAAqB;kBACzBpB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACvD;gBAED;kBAAA/B,QAAA,EAAAgC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxE,OAAA;kBACEyE,OAAO,EAAEA,CAAA,KAAMf,cAAc,CAACR,QAAQ,CAACS,EAAE,EAAET,QAAQ,CAACG,QAAQ,CAAE;kBAC9De,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C;gBAED;kBAAA/B,QAAA,EAAAgC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAlC,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA7BEtB,QAAQ,CAACS,EAAE;cAAArB,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8BhB,CACL;UAAC;YAAAlC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAlC,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAlC,QAAA,EAAAgC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA9SID,kBAA4B;AAAAwF,EAAA,GAA5BxF,kBAA4B;AAgTlC,eAAeA,kBAAkB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}