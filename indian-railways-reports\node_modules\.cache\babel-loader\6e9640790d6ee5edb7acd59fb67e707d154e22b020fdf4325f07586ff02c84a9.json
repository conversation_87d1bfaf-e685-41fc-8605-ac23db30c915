{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n// @ts-ignore\nimport nodeFetch, { Headers as NodeFetchHeaders } from '@supabase/node-fetch';\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = nodeFetch;\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const resolveHeadersConstructor = () => {\n  if (typeof Headers === 'undefined') {\n    return NodeFetchHeaders;\n  }\n  return Headers;\n};\nexport const fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n  const fetch = resolveFetch(customFetch);\n  const HeadersConstructor = resolveHeadersConstructor();\n  return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const accessToken = (_a = yield getAccessToken()) !== null && _a !== void 0 ? _a : supabaseKey;\n    let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n    if (!headers.has('apikey')) {\n      headers.set('apikey', supabaseKey);\n    }\n    if (!headers.has('Authorization')) {\n      headers.set('Authorization', `Bearer ${accessToken}`);\n    }\n    return fetch(input, Object.assign(Object.assign({}, init), {\n      headers\n    }));\n  });\n};", "map": {"version": 3, "names": ["nodeFetch", "Headers", "NodeFetchHeaders", "resolveFetch", "customFetch", "_fetch", "fetch", "args", "resolveHeadersConstructor", "fetchWithAuth", "supabase<PERSON>ey", "getAccessToken", "HeadersConstructor", "input", "init", "__awaiter", "accessToken", "_a", "headers", "has", "set", "Object", "assign"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\supabase-js\\src\\lib\\fetch.ts"], "sourcesContent": ["// @ts-ignore\nimport nodeFetch, { Headers as NodeFetchHeaders } from '@supabase/node-fetch'\n\ntype Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = nodeFetch as unknown as Fetch\n  } else {\n    _fetch = fetch\n  }\n  return (...args: Parameters<Fetch>) => _fetch(...args)\n}\n\nexport const resolveHeadersConstructor = () => {\n  if (typeof Headers === 'undefined') {\n    return NodeFetchHeaders\n  }\n\n  return Headers\n}\n\nexport const fetchWithAuth = (\n  supabaseKey: string,\n  getAccessToken: () => Promise<string | null>,\n  customFetch?: Fetch\n): Fetch => {\n  const fetch = resolveFetch(customFetch)\n  const HeadersConstructor = resolveHeadersConstructor()\n\n  return async (input, init) => {\n    const accessToken = (await getAccessToken()) ?? supabaseKey\n    let headers = new HeadersConstructor(init?.headers)\n\n    if (!headers.has('apikey')) {\n      headers.set('apikey', supabaseKey)\n    }\n\n    if (!headers.has('Authorization')) {\n      headers.set('Authorization', `Bearer ${accessToken}`)\n    }\n\n    return fetch(input, { ...init, headers })\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,OAAOA,SAAS,IAAIC,OAAO,IAAIC,gBAAgB,QAAQ,sBAAsB;AAI7E,OAAO,MAAMC,YAAY,GAAIC,WAAmB,IAAW;EACzD,IAAIC,MAAa;EACjB,IAAID,WAAW,EAAE;IACfC,MAAM,GAAGD,WAAW;GACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACvCD,MAAM,GAAGL,SAA6B;GACvC,MAAM;IACLK,MAAM,GAAGC,KAAK;;EAEhB,OAAO,CAAC,GAAGC,IAAuB,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACxD,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAGA,CAAA,KAAK;EAC5C,IAAI,OAAOP,OAAO,KAAK,WAAW,EAAE;IAClC,OAAOC,gBAAgB;;EAGzB,OAAOD,OAAO;AAChB,CAAC;AAED,OAAO,MAAMQ,aAAa,GAAGA,CAC3BC,WAAmB,EACnBC,cAA4C,EAC5CP,WAAmB,KACV;EACT,MAAME,KAAK,GAAGH,YAAY,CAACC,WAAW,CAAC;EACvC,MAAMQ,kBAAkB,GAAGJ,yBAAyB,EAAE;EAEtD,OAAO,CAAOK,KAAK,EAAEC,IAAI,KAAIC,SAAA;;IAC3B,MAAMC,WAAW,GAAG,CAAAC,EAAA,GAAC,MAAMN,cAAc,EAAG,cAAAM,EAAA,cAAAA,EAAA,GAAIP,WAAW;IAC3D,IAAIQ,OAAO,GAAG,IAAIN,kBAAkB,CAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,CAAC;IAEnD,IAAI,CAACA,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAE;MAC1BD,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAEV,WAAW,CAAC;;IAGpC,IAAI,CAACQ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,EAAE;MACjCD,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE,UAAUJ,WAAW,EAAE,CAAC;;IAGvD,OAAOV,KAAK,CAACO,KAAK,EAAAQ,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOR,IAAI;MAAEI;IAAO,GAAG;EAC3C,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}