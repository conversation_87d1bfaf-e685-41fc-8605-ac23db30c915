{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"object\" == typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define([], t) : \"object\" == typeof exports ? exports.trimCanvas = t() : e.trimCanvas = t();\n}(this, function () {\n  return function (e) {\n    function t(n) {\n      if (r[n]) return r[n].exports;\n      var o = r[n] = {\n        exports: {},\n        id: n,\n        loaded: !1\n      };\n      return e[n].call(o.exports, o, o.exports, t), o.loaded = !0, o.exports;\n    }\n    var r = {};\n    return t.m = e, t.c = r, t.p = \"\", t(0);\n  }([function (e, t) {\n    \"use strict\";\n\n    function r(e) {\n      var t = e.getContext(\"2d\"),\n        r = e.width,\n        n = e.height,\n        o = t.getImageData(0, 0, r, n).data,\n        f = a(!0, r, n, o),\n        i = a(!1, r, n, o),\n        c = u(!0, r, n, o),\n        d = u(!1, r, n, o),\n        p = d - c + 1,\n        l = i - f + 1,\n        s = t.getImageData(c, f, p, l);\n      return e.width = p, e.height = l, t.clearRect(0, 0, p, l), t.putImageData(s, 0, 0), e;\n    }\n    function n(e, t, r, n) {\n      return {\n        red: n[4 * (r * t + e)],\n        green: n[4 * (r * t + e) + 1],\n        blue: n[4 * (r * t + e) + 2],\n        alpha: n[4 * (r * t + e) + 3]\n      };\n    }\n    function o(e, t, r, o) {\n      return n(e, t, r, o).alpha;\n    }\n    function a(e, t, r, n) {\n      for (var a = e ? 1 : -1, u = e ? 0 : r - 1, f = u; e ? f < r : f > -1; f += a) for (var i = 0; i < t; i++) if (o(i, f, t, n)) return f;\n      return null;\n    }\n    function u(e, t, r, n) {\n      for (var a = e ? 1 : -1, u = e ? 0 : t - 1, f = u; e ? f < t : f > -1; f += a) for (var i = 0; i < r; i++) if (o(f, i, t, n)) return f;\n      return null;\n    }\n    Object.defineProperty(t, \"__esModule\", {\n      value: !0\n    }), t.default = r;\n  }]);\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "trimCanvas", "n", "r", "o", "id", "loaded", "call", "m", "c", "p", "getContext", "width", "height", "getImageData", "data", "f", "a", "i", "u", "d", "l", "s", "clearRect", "putImageData", "red", "green", "blue", "alpha", "Object", "defineProperty", "value", "default"], "sources": ["C:/Doc Maker AI/indian-railways-reports/node_modules/trim-canvas/build/index.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.trimCanvas=t():e.trimCanvas=t()}(this,function(){return function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={exports:{},id:n,loaded:!1};return e[n].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var r={};return t.m=e,t.c=r,t.p=\"\",t(0)}([function(e,t){\"use strict\";function r(e){var t=e.getContext(\"2d\"),r=e.width,n=e.height,o=t.getImageData(0,0,r,n).data,f=a(!0,r,n,o),i=a(!1,r,n,o),c=u(!0,r,n,o),d=u(!1,r,n,o),p=d-c+1,l=i-f+1,s=t.getImageData(c,f,p,l);return e.width=p,e.height=l,t.clearRect(0,0,p,l),t.putImageData(s,0,0),e}function n(e,t,r,n){return{red:n[4*(r*t+e)],green:n[4*(r*t+e)+1],blue:n[4*(r*t+e)+2],alpha:n[4*(r*t+e)+3]}}function o(e,t,r,o){return n(e,t,r,o).alpha}function a(e,t,r,n){for(var a=e?1:-1,u=e?0:r-1,f=u;e?f<r:f>-1;f+=a)for(var i=0;i<t;i++)if(o(i,f,t,n))return f;return null}function u(e,t,r,n){for(var a=e?1:-1,u=e?0:t-1,f=u;e?f<t:f>-1;f+=a)for(var i=0;i<r;i++)if(o(f,i,t,n))return f;return null}Object.defineProperty(t,\"__esModule\",{value:!0}),t.default=r}])});"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,QAAQ,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAAC,EAAE,EAACH,CAAC,CAAC,GAAC,QAAQ,IAAE,OAAOC,OAAO,GAACA,OAAO,CAACI,UAAU,GAACL,CAAC,CAAC,CAAC,GAACD,CAAC,CAACM,UAAU,GAACL,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAC,YAAU;EAAC,OAAO,UAASD,CAAC,EAAC;IAAC,SAASC,CAACA,CAACM,CAAC,EAAC;MAAC,IAAGC,CAAC,CAACD,CAAC,CAAC,EAAC,OAAOC,CAAC,CAACD,CAAC,CAAC,CAACL,OAAO;MAAC,IAAIO,CAAC,GAACD,CAAC,CAACD,CAAC,CAAC,GAAC;QAACL,OAAO,EAAC,CAAC,CAAC;QAACQ,EAAE,EAACH,CAAC;QAACI,MAAM,EAAC,CAAC;MAAC,CAAC;MAAC,OAAOX,CAAC,CAACO,CAAC,CAAC,CAACK,IAAI,CAACH,CAAC,CAACP,OAAO,EAACO,CAAC,EAACA,CAAC,CAACP,OAAO,EAACD,CAAC,CAAC,EAACQ,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,EAACF,CAAC,CAACP,OAAO;IAAA;IAAC,IAAIM,CAAC,GAAC,CAAC,CAAC;IAAC,OAAOP,CAAC,CAACY,CAAC,GAACb,CAAC,EAACC,CAAC,CAACa,CAAC,GAACN,CAAC,EAACP,CAAC,CAACc,CAAC,GAAC,EAAE,EAACd,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,UAASD,CAAC,EAACC,CAAC,EAAC;IAAC,YAAY;;IAAC,SAASO,CAACA,CAACR,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACD,CAAC,CAACgB,UAAU,CAAC,IAAI,CAAC;QAACR,CAAC,GAACR,CAAC,CAACiB,KAAK;QAACV,CAAC,GAACP,CAAC,CAACkB,MAAM;QAACT,CAAC,GAACR,CAAC,CAACkB,YAAY,CAAC,CAAC,EAAC,CAAC,EAACX,CAAC,EAACD,CAAC,CAAC,CAACa,IAAI;QAACC,CAAC,GAACC,CAAC,CAAC,CAAC,CAAC,EAACd,CAAC,EAACD,CAAC,EAACE,CAAC,CAAC;QAACc,CAAC,GAACD,CAAC,CAAC,CAAC,CAAC,EAACd,CAAC,EAACD,CAAC,EAACE,CAAC,CAAC;QAACK,CAAC,GAACU,CAAC,CAAC,CAAC,CAAC,EAAChB,CAAC,EAACD,CAAC,EAACE,CAAC,CAAC;QAACgB,CAAC,GAACD,CAAC,CAAC,CAAC,CAAC,EAAChB,CAAC,EAACD,CAAC,EAACE,CAAC,CAAC;QAACM,CAAC,GAACU,CAAC,GAACX,CAAC,GAAC,CAAC;QAACY,CAAC,GAACH,CAAC,GAACF,CAAC,GAAC,CAAC;QAACM,CAAC,GAAC1B,CAAC,CAACkB,YAAY,CAACL,CAAC,EAACO,CAAC,EAACN,CAAC,EAACW,CAAC,CAAC;MAAC,OAAO1B,CAAC,CAACiB,KAAK,GAACF,CAAC,EAACf,CAAC,CAACkB,MAAM,GAACQ,CAAC,EAACzB,CAAC,CAAC2B,SAAS,CAAC,CAAC,EAAC,CAAC,EAACb,CAAC,EAACW,CAAC,CAAC,EAACzB,CAAC,CAAC4B,YAAY,CAACF,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC3B,CAAC;IAAA;IAAC,SAASO,CAACA,CAACP,CAAC,EAACC,CAAC,EAACO,CAAC,EAACD,CAAC,EAAC;MAAC,OAAM;QAACuB,GAAG,EAACvB,CAAC,CAAC,CAAC,IAAEC,CAAC,GAACP,CAAC,GAACD,CAAC,CAAC,CAAC;QAAC+B,KAAK,EAACxB,CAAC,CAAC,CAAC,IAAEC,CAAC,GAACP,CAAC,GAACD,CAAC,CAAC,GAAC,CAAC,CAAC;QAACgC,IAAI,EAACzB,CAAC,CAAC,CAAC,IAAEC,CAAC,GAACP,CAAC,GAACD,CAAC,CAAC,GAAC,CAAC,CAAC;QAACiC,KAAK,EAAC1B,CAAC,CAAC,CAAC,IAAEC,CAAC,GAACP,CAAC,GAACD,CAAC,CAAC,GAAC,CAAC;MAAC,CAAC;IAAA;IAAC,SAASS,CAACA,CAACT,CAAC,EAACC,CAAC,EAACO,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOF,CAAC,CAACP,CAAC,EAACC,CAAC,EAACO,CAAC,EAACC,CAAC,CAAC,CAACwB,KAAK;IAAA;IAAC,SAASX,CAACA,CAACtB,CAAC,EAACC,CAAC,EAACO,CAAC,EAACD,CAAC,EAAC;MAAC,KAAI,IAAIe,CAAC,GAACtB,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,EAACwB,CAAC,GAACxB,CAAC,GAAC,CAAC,GAACQ,CAAC,GAAC,CAAC,EAACa,CAAC,GAACG,CAAC,EAACxB,CAAC,GAACqB,CAAC,GAACb,CAAC,GAACa,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAEC,CAAC,EAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACtB,CAAC,EAACsB,CAAC,EAAE,EAAC,IAAGd,CAAC,CAACc,CAAC,EAACF,CAAC,EAACpB,CAAC,EAACM,CAAC,CAAC,EAAC,OAAOc,CAAC;MAAC,OAAO,IAAI;IAAA;IAAC,SAASG,CAACA,CAACxB,CAAC,EAACC,CAAC,EAACO,CAAC,EAACD,CAAC,EAAC;MAAC,KAAI,IAAIe,CAAC,GAACtB,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,EAACwB,CAAC,GAACxB,CAAC,GAAC,CAAC,GAACC,CAAC,GAAC,CAAC,EAACoB,CAAC,GAACG,CAAC,EAACxB,CAAC,GAACqB,CAAC,GAACpB,CAAC,GAACoB,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAEC,CAAC,EAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACf,CAAC,EAACe,CAAC,EAAE,EAAC,IAAGd,CAAC,CAACY,CAAC,EAACE,CAAC,EAACtB,CAAC,EAACM,CAAC,CAAC,EAAC,OAAOc,CAAC;MAAC,OAAO,IAAI;IAAA;IAACa,MAAM,CAACC,cAAc,CAAClC,CAAC,EAAC,YAAY,EAAC;MAACmC,KAAK,EAAC,CAAC;IAAC,CAAC,CAAC,EAACnC,CAAC,CAACoC,OAAO,GAAC7B,CAAC;EAAA,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}