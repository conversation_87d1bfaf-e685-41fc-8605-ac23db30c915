[{"C:\\Doc Maker AI\\indian-railways-reports\\src\\index.tsx": "1", "C:\\Doc Maker AI\\indian-railways-reports\\src\\App.tsx": "2", "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\TemplateManagement.tsx": "3", "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\Dashboard.tsx": "4", "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\CreateReport.tsx": "5", "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\ReportView.tsx": "6", "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\Header.tsx": "7", "C:\\Doc Maker AI\\indian-railways-reports\\src\\lib\\supabase.ts": "8", "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\SignatureCapture.tsx": "9"}, {"size": 273, "mtime": 1753977079463, "results": "10", "hashOfConfig": "11"}, {"size": 902, "mtime": 1753977332279, "results": "12", "hashOfConfig": "11"}, {"size": 10670, "mtime": 1753977538138, "results": "13", "hashOfConfig": "11"}, {"size": 6290, "mtime": 1753977500524, "results": "14", "hashOfConfig": "11"}, {"size": 11621, "mtime": 1753977824429, "results": "15", "hashOfConfig": "11"}, {"size": 7703, "mtime": 1753977634857, "results": "16", "hashOfConfig": "11"}, {"size": 1208, "mtime": 1753977340516, "results": "17", "hashOfConfig": "11"}, {"size": 1040, "mtime": 1753984226684, "results": "18", "hashOfConfig": "11"}, {"size": 3037, "mtime": 1753977739133, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dlm99t", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Doc Maker AI\\indian-railways-reports\\src\\index.tsx", [], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\App.tsx", [], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\TemplateManagement.tsx", ["47", "48", "49"], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\Dashboard.tsx", [], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\CreateReport.tsx", ["50", "51", "52"], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\ReportView.tsx", [], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\Header.tsx", [], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\lib\\supabase.ts", [], [], "C:\\Doc Maker AI\\indian-railways-reports\\src\\components\\SignatureCapture.tsx", [], [], {"ruleId": "53", "severity": 1, "message": "54", "line": 62, "column": 21, "nodeType": "55", "messageId": "56", "endLine": 62, "endColumn": 31}, {"ruleId": "53", "severity": 1, "message": "57", "line": 77, "column": 21, "nodeType": "55", "messageId": "56", "endLine": 77, "endColumn": 29}, {"ruleId": "53", "severity": 1, "message": "58", "line": 109, "column": 11, "nodeType": "55", "messageId": "56", "endLine": 109, "endColumn": 47}, {"ruleId": "53", "severity": 1, "message": "59", "line": 3, "column": 30, "nodeType": "55", "messageId": "56", "endLine": 3, "endColumn": 36}, {"ruleId": "53", "severity": 1, "message": "54", "line": 100, "column": 21, "nodeType": "55", "messageId": "56", "endLine": 100, "endColumn": 31}, {"ruleId": "53", "severity": 1, "message": "60", "line": 149, "column": 13, "nodeType": "55", "messageId": "56", "endLine": 149, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'uploadData' is assigned a value but never used.", "Identifier", "unusedVar", "'template' is assigned a value but never used.", "'placeholders' is assigned a value but never used.", "'Report' is defined but never used.", "'result' is assigned a value but never used."]