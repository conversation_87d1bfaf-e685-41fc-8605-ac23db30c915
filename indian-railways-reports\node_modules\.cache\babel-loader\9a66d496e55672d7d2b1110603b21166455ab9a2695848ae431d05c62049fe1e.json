{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://lexnztbclzychlvrauhq.supabase.co';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase"], "sources": ["C:/Doc Maker AI/indian-railways-reports/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://lexnztbclzychlvrauhq.supabase.co'\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY'\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface User {\n  id: string\n  email?: string\n  created_at: string\n}\n\nexport interface Template {\n  id: string\n  user_id?: string\n  name: string\n  type: 'joint_report' | 'ta_form'\n  file_url: string\n  placeholder_map?: Record<string, string>\n  created_at: string\n}\n\nexport interface Report {\n  id: string\n  user_id?: string\n  template_id: string\n  data: Record<string, any>\n  generated_doc_url?: string\n  generated_pdf_url?: string\n  signature_url?: string\n  status: 'draft' | 'generated'\n  created_at: string\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AAEpD,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,0CAA0C;AACpG,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,kNAAkN;AAErR,OAAO,MAAMC,QAAQ,GAAGP,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC;;AAElE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}