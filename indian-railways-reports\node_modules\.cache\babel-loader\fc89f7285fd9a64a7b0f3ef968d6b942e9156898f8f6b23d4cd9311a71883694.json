{"ast": null, "code": "import { API_VERSION_HEADER_NAME, BASE64URL_REGEX } from './constants';\nimport { AuthInvalidJwtError } from './errors';\nimport { base64UrlToUint8Array, stringFromBase64URL } from './base64url';\nexport function expiresAt(expiresIn) {\n  const timeNow = Math.round(Date.now() / 1000);\n  return timeNow + expiresIn;\n}\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport const isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false;\n  }\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false;\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false;\n  }\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable;\n  }\n  const randomKey = `lswt-${Math.random()}${Math.random()}`;\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey);\n    globalThis.localStorage.removeItem(randomKey);\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = true;\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = false;\n  }\n  return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href) {\n  const result = {};\n  const url = new URL(href);\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value;\n      });\n    } catch (e) {\n      // hash is not a query string\n    }\n  }\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value;\n  });\n  return result;\n}\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const looksLikeFetchResponse = maybeResponse => {\n  return typeof maybeResponse === 'object' && maybeResponse !== null && 'status' in maybeResponse && 'ok' in maybeResponse && 'json' in maybeResponse && typeof maybeResponse.json === 'function';\n};\n// Storage helpers\nexport const setItemAsync = async (storage, key, data) => {\n  await storage.setItem(key, JSON.stringify(data));\n};\nexport const getItemAsync = async (storage, key) => {\n  const value = await storage.getItem(key);\n  if (!value) {\n    return null;\n  }\n  try {\n    return JSON.parse(value);\n  } catch (_a) {\n    return value;\n  }\n};\nexport const removeItemAsync = async (storage, key) => {\n  await storage.removeItem(key);\n};\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred {\n  constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    this.promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;\n      this.resolve = res;\n      this.reject = rej;\n    });\n  }\n}\nDeferred.promiseConstructor = Promise;\nexport function decodeJWT(token) {\n  const parts = token.split('.');\n  if (parts.length !== 3) {\n    throw new AuthInvalidJwtError('Invalid JWT structure');\n  }\n  // Regex checks for base64url format\n  for (let i = 0; i < parts.length; i++) {\n    if (!BASE64URL_REGEX.test(parts[i])) {\n      throw new AuthInvalidJwtError('JWT not in base64url format');\n    }\n  }\n  const data = {\n    // using base64url lib\n    header: JSON.parse(stringFromBase64URL(parts[0])),\n    payload: JSON.parse(stringFromBase64URL(parts[1])),\n    signature: base64UrlToUint8Array(parts[2]),\n    raw: {\n      header: parts[0],\n      payload: parts[1]\n    }\n  };\n  return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time) {\n  return await new Promise(accept => {\n    setTimeout(() => accept(null), time);\n  });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable(fn, isRetryable) {\n  const promise = new Promise((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    (async () => {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = await fn(attempt);\n          if (!isRetryable(attempt, null, result)) {\n            accept(result);\n            return;\n          }\n        } catch (e) {\n          if (!isRetryable(attempt, e)) {\n            reject(e);\n            return;\n          }\n        }\n      }\n    })();\n  });\n  return promise;\n}\nfunction dec2hex(dec) {\n  return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56;\n  const array = new Uint32Array(verifierLength);\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n    const charSetLen = charSet.length;\n    let verifier = '';\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n    }\n    return verifier;\n  }\n  crypto.getRandomValues(array);\n  return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n  const encoder = new TextEncoder();\n  const encodedData = encoder.encode(randomString);\n  const hash = await crypto.subtle.digest('SHA-256', encodedData);\n  const bytes = new Uint8Array(hash);\n  return Array.from(bytes).map(c => String.fromCharCode(c)).join('');\n}\nexport async function generatePKCEChallenge(verifier) {\n  const hasCryptoSupport = typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined' && typeof TextEncoder !== 'undefined';\n  if (!hasCryptoSupport) {\n    console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n    return verifier;\n  }\n  const hashed = await sha256(verifier);\n  return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nexport async function getCodeChallengeAndMethod(storage, storageKey, isPasswordRecovery = false) {\n  const codeVerifier = generatePKCEVerifier();\n  let storedCodeVerifier = codeVerifier;\n  if (isPasswordRecovery) {\n    storedCodeVerifier += '/PASSWORD_RECOVERY';\n  }\n  await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n  const codeChallenge = await generatePKCEChallenge(codeVerifier);\n  const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n  return [codeChallenge, codeChallengeMethod];\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nexport function parseResponseAPIVersion(response) {\n  const apiVersion = response.headers.get(API_VERSION_HEADER_NAME);\n  if (!apiVersion) {\n    return null;\n  }\n  if (!apiVersion.match(API_VERSION_REGEX)) {\n    return null;\n  }\n  try {\n    const date = new Date(`${apiVersion}T00:00:00.0Z`);\n    return date;\n  } catch (e) {\n    return null;\n  }\n}\nexport function validateExp(exp) {\n  if (!exp) {\n    throw new Error('Missing exp claim');\n  }\n  const timeNow = Math.floor(Date.now() / 1000);\n  if (exp <= timeNow) {\n    throw new Error('JWT has expired');\n  }\n}\nexport function getAlgorithm(alg) {\n  switch (alg) {\n    case 'RS256':\n      return {\n        name: 'RSASSA-PKCS1-v1_5',\n        hash: {\n          name: 'SHA-256'\n        }\n      };\n    case 'ES256':\n      return {\n        name: 'ECDSA',\n        namedCurve: 'P-256',\n        hash: {\n          name: 'SHA-256'\n        }\n      };\n    default:\n      throw new Error('Invalid alg claim');\n  }\n}\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;\nexport function validateUUID(str) {\n  if (!UUID_REGEX.test(str)) {\n    throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not');\n  }\n}\nexport function userNotAvailableProxy() {\n  const proxyTarget = {};\n  return new Proxy(proxyTarget, {\n    get: (target, prop) => {\n      if (prop === '__isUserNotAvailableProxy') {\n        return true;\n      }\n      // Preventative check for common problematic symbols during cloning/inspection\n      // These symbols might be accessed by structuredClone or other internal mechanisms.\n      if (typeof prop === 'symbol') {\n        const sProp = prop.toString();\n        if (sProp === 'Symbol(Symbol.toPrimitive)' || sProp === 'Symbol(Symbol.toStringTag)' || sProp === 'Symbol(util.inspect.custom)') {\n          // Node.js util.inspect\n          return undefined;\n        }\n      }\n      throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the \"${prop}\" property of the session object is not supported. Please use getUser() instead.`);\n    },\n    set: (_target, prop) => {\n      throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`);\n    },\n    deleteProperty: (_target, prop) => {\n      throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`);\n    }\n  });\n}\n/**\n * Deep clones a JSON-serializable object using JSON.parse(JSON.stringify(obj)).\n * Note: Only works for JSON-safe data.\n */\nexport function deepClone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n}", "map": {"version": 3, "names": ["API_VERSION_HEADER_NAME", "BASE64URL_REGEX", "AuthInvalidJwtError", "base64UrlToUint8Array", "stringFromBase64URL", "expiresAt", "expiresIn", "timeNow", "Math", "round", "Date", "now", "uuid", "replace", "c", "r", "random", "v", "toString", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "localStorageWriteTests", "tested", "writable", "supportsLocalStorage", "globalThis", "localStorage", "e", "<PERSON><PERSON><PERSON>", "setItem", "removeItem", "parseParametersFromURL", "href", "result", "url", "URL", "hash", "hashSearchParams", "URLSearchParams", "substring", "for<PERSON>ach", "value", "key", "searchParams", "resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "looksLikeFetchResponse", "maybeResponse", "json", "setItemAsync", "storage", "data", "JSON", "stringify", "getItemAsync", "getItem", "parse", "_a", "removeItemAsync", "Deferred", "constructor", "promise", "promiseConstructor", "res", "rej", "resolve", "reject", "Promise", "decodeJWT", "token", "parts", "split", "length", "i", "test", "header", "payload", "signature", "raw", "sleep", "time", "accept", "setTimeout", "retryable", "fn", "isRetryable", "attempt", "Infinity", "dec2hex", "dec", "substr", "generatePKCEVerifier", "verifier<PERSON><PERSON><PERSON>", "array", "Uint32Array", "crypto", "charSet", "charSetLen", "verifier", "char<PERSON>t", "floor", "getRandomValues", "Array", "from", "join", "sha256", "randomString", "encoder", "TextEncoder", "encodedData", "encode", "subtle", "digest", "bytes", "Uint8Array", "map", "String", "fromCharCode", "generatePKCEChallenge", "hasCryptoSupport", "console", "warn", "hashed", "btoa", "getCodeChallengeAndMethod", "storageKey", "isPasswordRecovery", "codeVerifier", "storedCodeVerifier", "codeChallenge", "codeChallengeMethod", "API_VERSION_REGEX", "parseResponseAPIVersion", "response", "apiVersion", "headers", "get", "match", "date", "validateExp", "exp", "Error", "getAlgorithm", "alg", "name", "namedCurve", "UUID_REGEX", "validateUUID", "str", "userNotAvailableProxy", "proxyTarget", "Proxy", "target", "prop", "sProp", "undefined", "set", "_target", "deleteProperty", "deepClone", "obj"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\auth-js\\src\\lib\\helpers.ts"], "sourcesContent": ["import { API_VERSION_HEADER_NAME, BASE64URL_REGEX } from './constants'\nimport { AuthInvalidJwtError } from './errors'\nimport { base64UrlToUint8Array, stringFromBase64URL } from './base64url'\nimport { JwtHeader, JwtPayload, SupportedStorage, User } from './types'\n\nexport function expiresAt(expiresIn: number) {\n  const timeNow = Math.round(Date.now() / 1000)\n  return timeNow + expiresIn\n}\n\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8\n    return v.toString(16)\n  })\n}\n\nexport const isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined'\n\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false,\n}\n\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false\n  }\n\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false\n  }\n\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable\n  }\n\n  const randomKey = `lswt-${Math.random()}${Math.random()}`\n\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey)\n    globalThis.localStorage.removeItem(randomKey)\n\n    localStorageWriteTests.tested = true\n    localStorageWriteTests.writable = true\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n\n    localStorageWriteTests.tested = true\n    localStorageWriteTests.writable = false\n  }\n\n  return localStorageWriteTests.writable\n}\n\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href: string) {\n  const result: { [parameter: string]: string } = {}\n\n  const url = new URL(href)\n\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1))\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value\n      })\n    } catch (e: any) {\n      // hash is not a query string\n    }\n  }\n\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value\n  })\n\n  return result\n}\n\ntype Fetch = typeof fetch\n\nexport const resolveFetch = (customFetch?: Fetch): Fetch => {\n  let _fetch: Fetch\n  if (customFetch) {\n    _fetch = customFetch\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) =>\n      import('@supabase/node-fetch' as any).then(({ default: fetch }) => fetch(...args))\n  } else {\n    _fetch = fetch\n  }\n  return (...args) => _fetch(...args)\n}\n\nexport const looksLikeFetchResponse = (maybeResponse: unknown): maybeResponse is Response => {\n  return (\n    typeof maybeResponse === 'object' &&\n    maybeResponse !== null &&\n    'status' in maybeResponse &&\n    'ok' in maybeResponse &&\n    'json' in maybeResponse &&\n    typeof (maybeResponse as any).json === 'function'\n  )\n}\n\n// Storage helpers\nexport const setItemAsync = async (\n  storage: SupportedStorage,\n  key: string,\n  data: any\n): Promise<void> => {\n  await storage.setItem(key, JSON.stringify(data))\n}\n\nexport const getItemAsync = async (storage: SupportedStorage, key: string): Promise<unknown> => {\n  const value = await storage.getItem(key)\n\n  if (!value) {\n    return null\n  }\n\n  try {\n    return JSON.parse(value)\n  } catch {\n    return value\n  }\n}\n\nexport const removeItemAsync = async (storage: SupportedStorage, key: string): Promise<void> => {\n  await storage.removeItem(key)\n}\n\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred<T = any> {\n  public static promiseConstructor: PromiseConstructor = Promise\n\n  public readonly promise!: PromiseLike<T>\n\n  public readonly resolve!: (value?: T | PromiseLike<T>) => void\n\n  public readonly reject!: (reason?: any) => any\n\n  public constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;(this as any).promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;(this as any).resolve = res\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;(this as any).reject = rej\n    })\n  }\n}\n\nexport function decodeJWT(token: string): {\n  header: JwtHeader\n  payload: JwtPayload\n  signature: Uint8Array\n  raw: {\n    header: string\n    payload: string\n  }\n} {\n  const parts = token.split('.')\n\n  if (parts.length !== 3) {\n    throw new AuthInvalidJwtError('Invalid JWT structure')\n  }\n\n  // Regex checks for base64url format\n  for (let i = 0; i < parts.length; i++) {\n    if (!BASE64URL_REGEX.test(parts[i] as string)) {\n      throw new AuthInvalidJwtError('JWT not in base64url format')\n    }\n  }\n  const data = {\n    // using base64url lib\n    header: JSON.parse(stringFromBase64URL(parts[0])),\n    payload: JSON.parse(stringFromBase64URL(parts[1])),\n    signature: base64UrlToUint8Array(parts[2]),\n    raw: {\n      header: parts[0],\n      payload: parts[1],\n    },\n  }\n  return data\n}\n\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time: number): Promise<null> {\n  return await new Promise((accept) => {\n    setTimeout(() => accept(null), time)\n  })\n}\n\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable<T>(\n  fn: (attempt: number) => Promise<T>,\n  isRetryable: (attempt: number, error: any | null, result?: T) => boolean\n): Promise<T> {\n  const promise = new Promise<T>((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;(async () => {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = await fn(attempt)\n\n          if (!isRetryable(attempt, null, result)) {\n            accept(result)\n            return\n          }\n        } catch (e: any) {\n          if (!isRetryable(attempt, e)) {\n            reject(e)\n            return\n          }\n        }\n      }\n    })()\n  })\n\n  return promise\n}\n\nfunction dec2hex(dec: number) {\n  return ('0' + dec.toString(16)).substr(-2)\n}\n\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56\n  const array = new Uint32Array(verifierLength)\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'\n    const charSetLen = charSet.length\n    let verifier = ''\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen))\n    }\n    return verifier\n  }\n  crypto.getRandomValues(array)\n  return Array.from(array, dec2hex).join('')\n}\n\nasync function sha256(randomString: string) {\n  const encoder = new TextEncoder()\n  const encodedData = encoder.encode(randomString)\n  const hash = await crypto.subtle.digest('SHA-256', encodedData)\n  const bytes = new Uint8Array(hash)\n\n  return Array.from(bytes)\n    .map((c) => String.fromCharCode(c))\n    .join('')\n}\n\nexport async function generatePKCEChallenge(verifier: string) {\n  const hasCryptoSupport =\n    typeof crypto !== 'undefined' &&\n    typeof crypto.subtle !== 'undefined' &&\n    typeof TextEncoder !== 'undefined'\n\n  if (!hasCryptoSupport) {\n    console.warn(\n      'WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.'\n    )\n    return verifier\n  }\n  const hashed = await sha256(verifier)\n  return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '')\n}\n\nexport async function getCodeChallengeAndMethod(\n  storage: SupportedStorage,\n  storageKey: string,\n  isPasswordRecovery = false\n) {\n  const codeVerifier = generatePKCEVerifier()\n  let storedCodeVerifier = codeVerifier\n  if (isPasswordRecovery) {\n    storedCodeVerifier += '/PASSWORD_RECOVERY'\n  }\n  await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier)\n  const codeChallenge = await generatePKCEChallenge(codeVerifier)\n  const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256'\n  return [codeChallenge, codeChallengeMethod]\n}\n\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i\n\nexport function parseResponseAPIVersion(response: Response) {\n  const apiVersion = response.headers.get(API_VERSION_HEADER_NAME)\n\n  if (!apiVersion) {\n    return null\n  }\n\n  if (!apiVersion.match(API_VERSION_REGEX)) {\n    return null\n  }\n\n  try {\n    const date = new Date(`${apiVersion}T00:00:00.0Z`)\n    return date\n  } catch (e: any) {\n    return null\n  }\n}\n\nexport function validateExp(exp: number) {\n  if (!exp) {\n    throw new Error('Missing exp claim')\n  }\n  const timeNow = Math.floor(Date.now() / 1000)\n  if (exp <= timeNow) {\n    throw new Error('JWT has expired')\n  }\n}\n\nexport function getAlgorithm(\n  alg: 'HS256' | 'RS256' | 'ES256'\n): RsaHashedImportParams | EcKeyImportParams {\n  switch (alg) {\n    case 'RS256':\n      return {\n        name: 'RSASSA-PKCS1-v1_5',\n        hash: { name: 'SHA-256' },\n      }\n    case 'ES256':\n      return {\n        name: 'ECDSA',\n        namedCurve: 'P-256',\n        hash: { name: 'SHA-256' },\n      }\n    default:\n      throw new Error('Invalid alg claim')\n  }\n}\n\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/\n\nexport function validateUUID(str: string) {\n  if (!UUID_REGEX.test(str)) {\n    throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not')\n  }\n}\n\nexport function userNotAvailableProxy(): User {\n  const proxyTarget = {} as User\n\n  return new Proxy(proxyTarget, {\n    get: (target: any, prop: string) => {\n      if (prop === '__isUserNotAvailableProxy') {\n        return true\n      }\n      // Preventative check for common problematic symbols during cloning/inspection\n      // These symbols might be accessed by structuredClone or other internal mechanisms.\n      if (typeof prop === 'symbol') {\n        const sProp = (prop as symbol).toString()\n        if (\n          sProp === 'Symbol(Symbol.toPrimitive)' ||\n          sProp === 'Symbol(Symbol.toStringTag)' ||\n          sProp === 'Symbol(util.inspect.custom)'\n        ) {\n          // Node.js util.inspect\n          return undefined\n        }\n      }\n      throw new Error(\n        `@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the \"${prop}\" property of the session object is not supported. Please use getUser() instead.`\n      )\n    },\n    set: (_target: any, prop: string) => {\n      throw new Error(\n        `@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`\n      )\n    },\n    deleteProperty: (_target: any, prop: string) => {\n      throw new Error(\n        `@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the \"${prop}\" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`\n      )\n    },\n  })\n}\n\n/**\n * Deep clones a JSON-serializable object using JSON.parse(JSON.stringify(obj)).\n * Note: Only works for JSON-safe data.\n */\nexport function deepClone<T>(obj: T): T {\n  return JSON.parse(JSON.stringify(obj))\n}\n"], "mappings": "AAAA,SAASA,uBAAuB,EAAEC,eAAe,QAAQ,aAAa;AACtE,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,qBAAqB,EAAEC,mBAAmB,QAAQ,aAAa;AAGxE,OAAM,SAAUC,SAASA,CAACC,SAAiB;EACzC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;EAC7C,OAAOJ,OAAO,GAAGD,SAAS;AAC5B;AAEA,OAAM,SAAUM,IAAIA,CAAA;EAClB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC;IACxE,MAAMC,CAAC,GAAIP,IAAI,CAACQ,MAAM,EAAE,GAAG,EAAE,GAAI,CAAC;MAChCC,CAAC,GAAGH,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACpC,OAAOE,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ;AAEA,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW;AAE/F,MAAMC,sBAAsB,GAAG;EAC7BC,MAAM,EAAE,KAAK;EACbC,QAAQ,EAAE;CACX;AAED;;;AAGA,OAAO,MAAMC,oBAAoB,GAAGA,CAAA,KAAK;EACvC,IAAI,CAACN,SAAS,EAAE,EAAE;IAChB,OAAO,KAAK;;EAGd,IAAI;IACF,IAAI,OAAOO,UAAU,CAACC,YAAY,KAAK,QAAQ,EAAE;MAC/C,OAAO,KAAK;;GAEf,CAAC,OAAOC,CAAC,EAAE;IACV;IACA,OAAO,KAAK;;EAGd,IAAIN,sBAAsB,CAACC,MAAM,EAAE;IACjC,OAAOD,sBAAsB,CAACE,QAAQ;;EAGxC,MAAMK,SAAS,GAAG,QAAQrB,IAAI,CAACQ,MAAM,EAAE,GAAGR,IAAI,CAACQ,MAAM,EAAE,EAAE;EAEzD,IAAI;IACFU,UAAU,CAACC,YAAY,CAACG,OAAO,CAACD,SAAS,EAAEA,SAAS,CAAC;IACrDH,UAAU,CAACC,YAAY,CAACI,UAAU,CAACF,SAAS,CAAC;IAE7CP,sBAAsB,CAACC,MAAM,GAAG,IAAI;IACpCD,sBAAsB,CAACE,QAAQ,GAAG,IAAI;GACvC,CAAC,OAAOI,CAAC,EAAE;IACV;IACA;IAEAN,sBAAsB,CAACC,MAAM,GAAG,IAAI;IACpCD,sBAAsB,CAACE,QAAQ,GAAG,KAAK;;EAGzC,OAAOF,sBAAsB,CAACE,QAAQ;AACxC,CAAC;AAED;;;AAGA,OAAM,SAAUQ,sBAAsBA,CAACC,IAAY;EACjD,MAAMC,MAAM,GAAoC,EAAE;EAElD,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACH,IAAI,CAAC;EAEzB,IAAIE,GAAG,CAACE,IAAI,IAAIF,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACnC,IAAI;MACF,MAAMC,gBAAgB,GAAG,IAAIC,eAAe,CAACJ,GAAG,CAACE,IAAI,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC;MACnEF,gBAAgB,CAACG,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;QACtCT,MAAM,CAACS,GAAG,CAAC,GAAGD,KAAK;MACrB,CAAC,CAAC;KACH,CAAC,OAAOd,CAAM,EAAE;MACf;IAAA;;EAIJ;EACAO,GAAG,CAACS,YAAY,CAACH,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;IACtCT,MAAM,CAACS,GAAG,CAAC,GAAGD,KAAK;EACrB,CAAC,CAAC;EAEF,OAAOR,MAAM;AACf;AAIA,OAAO,MAAMW,YAAY,GAAIC,WAAmB,IAAW;EACzD,IAAIC,MAAa;EACjB,IAAID,WAAW,EAAE;IACfC,MAAM,GAAGD,WAAW;GACrB,MAAM,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACvCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KACf,MAAM,CAAC,sBAA6B,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAEH;IAAK,CAAE,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;GACrF,MAAM;IACLF,MAAM,GAAGC,KAAK;;EAEhB,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACrC,CAAC;AAED,OAAO,MAAMG,sBAAsB,GAAIC,aAAsB,IAA+B;EAC1F,OACE,OAAOA,aAAa,KAAK,QAAQ,IACjCA,aAAa,KAAK,IAAI,IACtB,QAAQ,IAAIA,aAAa,IACzB,IAAI,IAAIA,aAAa,IACrB,MAAM,IAAIA,aAAa,IACvB,OAAQA,aAAqB,CAACC,IAAI,KAAK,UAAU;AAErD,CAAC;AAED;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAC1BC,OAAyB,EACzBb,GAAW,EACXc,IAAS,KACQ;EACjB,MAAMD,OAAO,CAAC1B,OAAO,CAACa,GAAG,EAAEe,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC;AAClD,CAAC;AAED,OAAO,MAAMG,YAAY,GAAG,MAAAA,CAAOJ,OAAyB,EAAEb,GAAW,KAAsB;EAC7F,MAAMD,KAAK,GAAG,MAAMc,OAAO,CAACK,OAAO,CAAClB,GAAG,CAAC;EAExC,IAAI,CAACD,KAAK,EAAE;IACV,OAAO,IAAI;;EAGb,IAAI;IACF,OAAOgB,IAAI,CAACI,KAAK,CAACpB,KAAK,CAAC;GACzB,CAAC,OAAAqB,EAAA,EAAM;IACN,OAAOrB,KAAK;;AAEhB,CAAC;AAED,OAAO,MAAMsB,eAAe,GAAG,MAAAA,CAAOR,OAAyB,EAAEb,GAAW,KAAmB;EAC7F,MAAMa,OAAO,CAACzB,UAAU,CAACY,GAAG,CAAC;AAC/B,CAAC;AAED;;;;;AAKA,OAAM,MAAOsB,QAAQ;EASnBC,YAAA;IACE;IACA;IAAE,IAAY,CAACC,OAAO,GAAG,IAAIF,QAAQ,CAACG,kBAAkB,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;MACpE;MACA;MAAE,IAAY,CAACC,OAAO,GAAGF,GAAG;MAE1B,IAAY,CAACG,MAAM,GAAGF,GAAG;IAC7B,CAAC,CAAC;EACJ;;AAhBcL,QAAA,CAAAG,kBAAkB,GAAuBK,OAAO;AAmBhE,OAAM,SAAUC,SAASA,CAACC,KAAa;EASrC,MAAMC,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;EAE9B,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACtB,MAAM,IAAI5E,mBAAmB,CAAC,uBAAuB,CAAC;;EAGxD;EACA,KAAK,IAAI6E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAI,CAAC9E,eAAe,CAAC+E,IAAI,CAACJ,KAAK,CAACG,CAAC,CAAW,CAAC,EAAE;MAC7C,MAAM,IAAI7E,mBAAmB,CAAC,6BAA6B,CAAC;;;EAGhE,MAAMuD,IAAI,GAAG;IACX;IACAwB,MAAM,EAAEvB,IAAI,CAACI,KAAK,CAAC1D,mBAAmB,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACjDM,OAAO,EAAExB,IAAI,CAACI,KAAK,CAAC1D,mBAAmB,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAClDO,SAAS,EAAEhF,qBAAqB,CAACyE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CQ,GAAG,EAAE;MACHH,MAAM,EAAEL,KAAK,CAAC,CAAC,CAAC;MAChBM,OAAO,EAAEN,KAAK,CAAC,CAAC;;GAEnB;EACD,OAAOnB,IAAI;AACb;AAEA;;;AAGA,OAAO,eAAe4B,KAAKA,CAACC,IAAY;EACtC,OAAO,MAAM,IAAIb,OAAO,CAAEc,MAAM,IAAI;IAClCC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAI,CAAC,EAAED,IAAI,CAAC;EACtC,CAAC,CAAC;AACJ;AAEA;;;;;AAKA,OAAM,SAAUG,SAASA,CACvBC,EAAmC,EACnCC,WAAwE;EAExE,MAAMxB,OAAO,GAAG,IAAIM,OAAO,CAAI,CAACc,MAAM,EAAEf,MAAM,KAAI;IAChD;IACA;IAAC,CAAC,YAAW;MACX,KAAK,IAAIoB,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGC,QAAQ,EAAED,OAAO,EAAE,EAAE;QACnD,IAAI;UACF,MAAM1D,MAAM,GAAG,MAAMwD,EAAE,CAACE,OAAO,CAAC;UAEhC,IAAI,CAACD,WAAW,CAACC,OAAO,EAAE,IAAI,EAAE1D,MAAM,CAAC,EAAE;YACvCqD,MAAM,CAACrD,MAAM,CAAC;YACd;;SAEH,CAAC,OAAON,CAAM,EAAE;UACf,IAAI,CAAC+D,WAAW,CAACC,OAAO,EAAEhE,CAAC,CAAC,EAAE;YAC5B4C,MAAM,CAAC5C,CAAC,CAAC;YACT;;;;IAIR,CAAC,EAAC,CAAE;EACN,CAAC,CAAC;EAEF,OAAOuC,OAAO;AAChB;AAEA,SAAS2B,OAAOA,CAACC,GAAW;EAC1B,OAAO,CAAC,GAAG,GAAGA,GAAG,CAAC7E,QAAQ,CAAC,EAAE,CAAC,EAAE8E,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5C;AAEA;AACA,OAAM,SAAUC,oBAAoBA,CAAA;EAClC,MAAMC,cAAc,GAAG,EAAE;EACzB,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAACF,cAAc,CAAC;EAC7C,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;IACjC,MAAMC,OAAO,GAAG,oEAAoE;IACpF,MAAMC,UAAU,GAAGD,OAAO,CAACxB,MAAM;IACjC,IAAI0B,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,cAAc,EAAEnB,CAAC,EAAE,EAAE;MACvCyB,QAAQ,IAAIF,OAAO,CAACG,MAAM,CAACjG,IAAI,CAACkG,KAAK,CAAClG,IAAI,CAACQ,MAAM,EAAE,GAAGuF,UAAU,CAAC,CAAC;;IAEpE,OAAOC,QAAQ;;EAEjBH,MAAM,CAACM,eAAe,CAACR,KAAK,CAAC;EAC7B,OAAOS,KAAK,CAACC,IAAI,CAACV,KAAK,EAAEL,OAAO,CAAC,CAACgB,IAAI,CAAC,EAAE,CAAC;AAC5C;AAEA,eAAeC,MAAMA,CAACC,YAAoB;EACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,EAAE;EACjC,MAAMC,WAAW,GAAGF,OAAO,CAACG,MAAM,CAACJ,YAAY,CAAC;EAChD,MAAM3E,IAAI,GAAG,MAAMgE,MAAM,CAACgB,MAAM,CAACC,MAAM,CAAC,SAAS,EAAEH,WAAW,CAAC;EAC/D,MAAMI,KAAK,GAAG,IAAIC,UAAU,CAACnF,IAAI,CAAC;EAElC,OAAOuE,KAAK,CAACC,IAAI,CAACU,KAAK,CAAC,CACrBE,GAAG,CAAE3G,CAAC,IAAK4G,MAAM,CAACC,YAAY,CAAC7G,CAAC,CAAC,CAAC,CAClCgG,IAAI,CAAC,EAAE,CAAC;AACb;AAEA,OAAO,eAAec,qBAAqBA,CAACpB,QAAgB;EAC1D,MAAMqB,gBAAgB,GACpB,OAAOxB,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACgB,MAAM,KAAK,WAAW,IACpC,OAAOH,WAAW,KAAK,WAAW;EAEpC,IAAI,CAACW,gBAAgB,EAAE;IACrBC,OAAO,CAACC,IAAI,CACV,oGAAoG,CACrG;IACD,OAAOvB,QAAQ;;EAEjB,MAAMwB,MAAM,GAAG,MAAMjB,MAAM,CAACP,QAAQ,CAAC;EACrC,OAAOyB,IAAI,CAACD,MAAM,CAAC,CAACnH,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAChF;AAEA,OAAO,eAAeqH,yBAAyBA,CAC7C1E,OAAyB,EACzB2E,UAAkB,EAClBC,kBAAkB,GAAG,KAAK;EAE1B,MAAMC,YAAY,GAAGpC,oBAAoB,EAAE;EAC3C,IAAIqC,kBAAkB,GAAGD,YAAY;EACrC,IAAID,kBAAkB,EAAE;IACtBE,kBAAkB,IAAI,oBAAoB;;EAE5C,MAAM/E,YAAY,CAACC,OAAO,EAAE,GAAG2E,UAAU,gBAAgB,EAAEG,kBAAkB,CAAC;EAC9E,MAAMC,aAAa,GAAG,MAAMX,qBAAqB,CAACS,YAAY,CAAC;EAC/D,MAAMG,mBAAmB,GAAGH,YAAY,KAAKE,aAAa,GAAG,OAAO,GAAG,MAAM;EAC7E,OAAO,CAACA,aAAa,EAAEC,mBAAmB,CAAC;AAC7C;AAEA;AACA,MAAMC,iBAAiB,GAAG,4DAA4D;AAEtF,OAAM,SAAUC,uBAAuBA,CAACC,QAAkB;EACxD,MAAMC,UAAU,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAAC9I,uBAAuB,CAAC;EAEhE,IAAI,CAAC4I,UAAU,EAAE;IACf,OAAO,IAAI;;EAGb,IAAI,CAACA,UAAU,CAACG,KAAK,CAACN,iBAAiB,CAAC,EAAE;IACxC,OAAO,IAAI;;EAGb,IAAI;IACF,MAAMO,IAAI,GAAG,IAAItI,IAAI,CAAC,GAAGkI,UAAU,cAAc,CAAC;IAClD,OAAOI,IAAI;GACZ,CAAC,OAAOpH,CAAM,EAAE;IACf,OAAO,IAAI;;AAEf;AAEA,OAAM,SAAUqH,WAAWA,CAACC,GAAW;EACrC,IAAI,CAACA,GAAG,EAAE;IACR,MAAM,IAAIC,KAAK,CAAC,mBAAmB,CAAC;;EAEtC,MAAM5I,OAAO,GAAGC,IAAI,CAACkG,KAAK,CAAChG,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;EAC7C,IAAIuI,GAAG,IAAI3I,OAAO,EAAE;IAClB,MAAM,IAAI4I,KAAK,CAAC,iBAAiB,CAAC;;AAEtC;AAEA,OAAM,SAAUC,YAAYA,CAC1BC,GAAgC;EAEhC,QAAQA,GAAG;IACT,KAAK,OAAO;MACV,OAAO;QACLC,IAAI,EAAE,mBAAmB;QACzBjH,IAAI,EAAE;UAAEiH,IAAI,EAAE;QAAS;OACxB;IACH,KAAK,OAAO;MACV,OAAO;QACLA,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,OAAO;QACnBlH,IAAI,EAAE;UAAEiH,IAAI,EAAE;QAAS;OACxB;IACH;MACE,MAAM,IAAIH,KAAK,CAAC,mBAAmB,CAAC;;AAE1C;AAEA,MAAMK,UAAU,GAAG,gEAAgE;AAEnF,OAAM,SAAUC,YAAYA,CAACC,GAAW;EACtC,IAAI,CAACF,UAAU,CAACxE,IAAI,CAAC0E,GAAG,CAAC,EAAE;IACzB,MAAM,IAAIP,KAAK,CAAC,6DAA6D,CAAC;;AAElF;AAEA,OAAM,SAAUQ,qBAAqBA,CAAA;EACnC,MAAMC,WAAW,GAAG,EAAU;EAE9B,OAAO,IAAIC,KAAK,CAACD,WAAW,EAAE;IAC5Bd,GAAG,EAAEA,CAACgB,MAAW,EAAEC,IAAY,KAAI;MACjC,IAAIA,IAAI,KAAK,2BAA2B,EAAE;QACxC,OAAO,IAAI;;MAEb;MACA;MACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAMC,KAAK,GAAID,IAAe,CAAC7I,QAAQ,EAAE;QACzC,IACE8I,KAAK,KAAK,4BAA4B,IACtCA,KAAK,KAAK,4BAA4B,IACtCA,KAAK,KAAK,6BAA6B,EACvC;UACA;UACA,OAAOC,SAAS;;;MAGpB,MAAM,IAAId,KAAK,CACb,kIAAkIY,IAAI,kFAAkF,CACzN;IACH,CAAC;IACDG,GAAG,EAAEA,CAACC,OAAY,EAAEJ,IAAY,KAAI;MAClC,MAAM,IAAIZ,KAAK,CACb,gIAAgIY,IAAI,oHAAoH,CACzP;IACH,CAAC;IACDK,cAAc,EAAEA,CAACD,OAAY,EAAEJ,IAAY,KAAI;MAC7C,MAAM,IAAIZ,KAAK,CACb,iIAAiIY,IAAI,oHAAoH,CAC1P;IACH;GACD,CAAC;AACJ;AAEA;;;;AAIA,OAAM,SAAUM,SAASA,CAAIC,GAAM;EACjC,OAAO5G,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,SAAS,CAAC2G,GAAG,CAAC,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}