{"ast": null, "code": "var _jsxFileName = \"C:\\\\Doc Maker AI\\\\indian-railways-reports\\\\src\\\\components\\\\Header.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-railway-blue text-white shadow-lg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold\",\n            children: \"Indian Railways\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-railway-orange\",\n            children: \"Report Generator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"hover:text-railway-orange transition-colors duration-200\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/templates\",\n            className: \"hover:text-railway-orange transition-colors duration-200\",\n            children: \"Templates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/create-report\",\n            className: \"hover:text-railway-orange transition-colors duration-200\",\n            children: \"Create Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Header", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Doc Maker AI/indian-railways-reports/src/components/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Header: React.FC = () => {\n  return (\n    <header className=\"bg-railway-blue text-white shadow-lg\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <h1 className=\"text-2xl font-bold\">Indian Railways</h1>\n            <span className=\"text-railway-orange\">Report Generator</span>\n          </div>\n          <nav className=\"flex space-x-6\">\n            <Link \n              to=\"/\" \n              className=\"hover:text-railway-orange transition-colors duration-200\"\n            >\n              Dashboard\n            </Link>\n            <Link \n              to=\"/templates\" \n              className=\"hover:text-railway-orange transition-colors duration-200\"\n            >\n              Templates\n            </Link>\n            <Link \n              to=\"/create-report\" \n              className=\"hover:text-railway-orange transition-colors duration-200\"\n            >\n              Create Report\n            </Link>\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA;IAAQE,SAAS,EAAC,sCAAsC;IAAAC,QAAA,eACtDH,OAAA;MAAKE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CH,OAAA;QAAKE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDH,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CH,OAAA;YAAIE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDP,OAAA;YAAME,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA,CAACF,IAAI;YACHU,EAAE,EAAC,GAAG;YACNN,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPP,OAAA,CAACF,IAAI;YACHU,EAAE,EAAC,YAAY;YACfN,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPP,OAAA,CAACF,IAAI;YACHU,EAAE,EAAC,gBAAgB;YACnBN,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACE,EAAA,GAjCIR,MAAgB;AAmCtB,eAAeA,MAAM;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}