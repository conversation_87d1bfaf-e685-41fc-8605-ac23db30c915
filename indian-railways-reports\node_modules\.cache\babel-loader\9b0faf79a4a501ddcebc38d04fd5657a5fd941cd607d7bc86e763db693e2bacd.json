{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(require(\"./PostgrestClient\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports.default = {\n  PostgrestClient: PostgrestClient_1.default,\n  PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n  PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n  PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n  PostgrestBuilder: PostgrestBuilder_1.default,\n  PostgrestError: PostgrestError_1.default\n};", "map": {"version": 3, "names": ["PostgrestClient_1", "__importDefault", "require", "exports", "PostgrestClient", "default", "PostgrestQueryBuilder_1", "PostgrestQueryBuilder", "PostgrestFilterBuilder_1", "PostgrestFilterBuilder", "PostgrestTransformBuilder_1", "PostgrestTransformBuilder", "PostgrestBuilder_1", "PostgrestBuilder", "PostgrestError_1", "PostgrestError"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\postgrest-js\\src\\index.ts"], "sourcesContent": ["// Always update wrapper.mjs when updating this file.\nimport PostgrestClient from './PostgrestClient'\nimport PostgrestQueryBuilder from './PostgrestQueryBuilder'\nimport PostgrestFilterBuilder from './PostgrestFilterBuilder'\nimport PostgrestTransformBuilder from './PostgrestTransformBuilder'\nimport PostgrestBuilder from './PostgrestBuilder'\nimport PostgrestError from './PostgrestError'\n\nexport {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\nexport type {\n  PostgrestResponse,\n  PostgrestResponseFailure,\n  PostgrestResponseSuccess,\n  PostgrestSingleResponse,\n  PostgrestMaybeSingleResponse,\n} from './types'\n// https://github.com/supabase/postgrest-js/issues/551\n// To be replaced with a helper type that only uses public types\nexport type { GetResult as UnstableGetResult } from './select-query-parser/result'\n"], "mappings": ";;;;;;;;;;;AAAA;AACA,MAAAA,iBAAA,GAAAC,eAAA,CAAAC,OAAA;AAQEC,OAAA,CAAAC,eAAA,GARKJ,iBAAA,CAAAK,OAAe;AACtB,MAAAC,uBAAA,GAAAL,eAAA,CAAAC,OAAA;AAQEC,OAAA,CAAAI,qBAAA,GARKD,uBAAA,CAAAD,OAAqB;AAC5B,MAAAG,wBAAA,GAAAP,eAAA,CAAAC,OAAA;AAQEC,OAAA,CAAAM,sBAAA,GARKD,wBAAA,CAAAH,OAAsB;AAC7B,MAAAK,2BAAA,GAAAT,eAAA,CAAAC,OAAA;AAQEC,OAAA,CAAAQ,yBAAA,GARKD,2BAAA,CAAAL,OAAyB;AAChC,MAAAO,kBAAA,GAAAX,eAAA,CAAAC,OAAA;AAQEC,OAAA,CAAAU,gBAAA,GARKD,kBAAA,CAAAP,OAAgB;AACvB,MAAAS,gBAAA,GAAAb,eAAA,CAAAC,OAAA;AAQEC,OAAA,CAAAY,cAAA,GARKD,gBAAA,CAAAT,OAAc;AAUrBF,OAAA,CAAAE,OAAA,GAAe;EACbD,eAAe,EAAfJ,iBAAA,CAAAK,OAAe;EACfE,qBAAqB,EAArBD,uBAAA,CAAAD,OAAqB;EACrBI,sBAAsB,EAAtBD,wBAAA,CAAAH,OAAsB;EACtBM,yBAAyB,EAAzBD,2BAAA,CAAAL,OAAyB;EACzBQ,gBAAgB,EAAhBD,kBAAA,CAAAP,OAAgB;EAChBU,cAAc,EAAdD,gBAAA,CAAAT;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}