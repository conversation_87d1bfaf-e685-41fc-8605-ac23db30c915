{"ast": null, "code": "import SupabaseClient from './SupabaseClient';\nexport * from '@supabase/auth-js';\nexport { PostgrestError } from '@supabase/postgrest-js';\nexport { FunctionsHttpError, FunctionsFetchError, FunctionsRelayError, FunctionsError, FunctionRegion } from '@supabase/functions-js';\nexport * from '@supabase/realtime-js';\nexport { default as SupabaseClient } from './SupabaseClient';\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = (supabaseUrl, supabaseKey, options) => {\n  return new SupabaseClient(supabaseUrl, supabaseKey, options);\n};\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning() {\n  if (typeof window !== 'undefined' || typeof process === 'undefined' || process.version === undefined || process.version === null) {\n    return false;\n  }\n  const versionMatch = process.version.match(/^v(\\d+)\\./);\n  if (!versionMatch) {\n    return false;\n  }\n  const majorVersion = parseInt(versionMatch[1], 10);\n  return majorVersion <= 18;\n}\nif (shouldShowDeprecationWarning()) {\n  console.warn(`⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` + `Please upgrade to Node.js 20 or later. ` + `For more information, visit: https://github.com/orgs/supabase/discussions/37217`);\n}", "map": {"version": 3, "names": ["SupabaseClient", "PostgrestError", "FunctionsHttpError", "FunctionsFetchError", "FunctionsRelayError", "FunctionsError", "FunctionRegion", "default", "createClient", "supabaseUrl", "supabase<PERSON>ey", "options", "shouldShowDeprecationWarning", "window", "process", "version", "undefined", "versionMatch", "match", "majorVersion", "parseInt", "console", "warn"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\supabase-js\\src\\index.ts"], "sourcesContent": ["import SupabaseClient from './SupabaseClient'\nimport type { GenericSchema, SupabaseClientOptions } from './lib/types'\n\nexport * from '@supabase/auth-js'\nexport type { User as AuthUser, Session as AuthSession } from '@supabase/auth-js'\nexport {\n  type PostgrestResponse,\n  type PostgrestSingleResponse,\n  type PostgrestMaybeSingleResponse,\n  PostgrestError,\n} from '@supabase/postgrest-js'\nexport {\n  FunctionsHttpError,\n  FunctionsFetchError,\n  FunctionsRelayError,\n  FunctionsError,\n  type FunctionInvokeOptions,\n  FunctionRegion,\n} from '@supabase/functions-js'\nexport * from '@supabase/realtime-js'\nexport { default as SupabaseClient } from './SupabaseClient'\nexport type { SupabaseClientOptions, QueryResult, QueryData, QueryError } from './lib/types'\n\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = <\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n>(\n  supabaseUrl: string,\n  supabaseKey: string,\n  options?: SupabaseClientOptions<SchemaName>\n): SupabaseClient<Database, SchemaName, Schema> => {\n  return new SupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, options)\n}\n\n// Check for Node.js <= 18 deprecation\nfunction shouldShowDeprecationWarning(): boolean {\n  if (\n    typeof window !== 'undefined' ||\n    typeof process === 'undefined' ||\n    process.version === undefined ||\n    process.version === null\n  ) {\n    return false\n  }\n\n  const versionMatch = process.version.match(/^v(\\d+)\\./)\n  if (!versionMatch) {\n    return false\n  }\n\n  const majorVersion = parseInt(versionMatch[1], 10)\n  return majorVersion <= 18\n}\n\nif (shouldShowDeprecationWarning()) {\n  console.warn(\n    `⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. ` +\n      `Please upgrade to Node.js 20 or later. ` +\n      `For more information, visit: https://github.com/orgs/supabase/discussions/37217`\n  )\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAG7C,cAAc,mBAAmB;AAEjC,SAIEC,cAAc,QACT,wBAAwB;AAC/B,SACEC,kBAAkB,EAClBC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EAEdC,cAAc,QACT,wBAAwB;AAC/B,cAAc,uBAAuB;AACrC,SAASC,OAAO,IAAIP,cAAc,QAAQ,kBAAkB;AAG5D;;;AAGA,OAAO,MAAMQ,YAAY,GAAGA,CAS1BC,WAAmB,EACnBC,WAAmB,EACnBC,OAA2C,KACK;EAChD,OAAO,IAAIX,cAAc,CAA+BS,WAAW,EAAEC,WAAW,EAAEC,OAAO,CAAC;AAC5F,CAAC;AAED;AACA,SAASC,4BAA4BA,CAAA;EACnC,IACE,OAAOC,MAAM,KAAK,WAAW,IAC7B,OAAOC,OAAO,KAAK,WAAW,IAC9BA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAC7BF,OAAO,CAACC,OAAO,KAAK,IAAI,EACxB;IACA,OAAO,KAAK;;EAGd,MAAME,YAAY,GAAGH,OAAO,CAACC,OAAO,CAACG,KAAK,CAAC,WAAW,CAAC;EACvD,IAAI,CAACD,YAAY,EAAE;IACjB,OAAO,KAAK;;EAGd,MAAME,YAAY,GAAGC,QAAQ,CAACH,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAClD,OAAOE,YAAY,IAAI,EAAE;AAC3B;AAEA,IAAIP,4BAA4B,EAAE,EAAE;EAClCS,OAAO,CAACC,IAAI,CACV,uHAAuH,GACrH,yCAAyC,GACzC,iFAAiF,CACpF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}