-- Indian Railways Report Generator Database Schema
-- This script sets up the complete database structure with RLS policies

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE report_status AS ENUM ('draft', 'generated');
CREATE TYPE template_type AS ENUM ('joint_report', 'ta_form');

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Templates table
CREATE TABLE IF NOT EXISTS public.templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type template_type NOT NULL,
    file_url TEXT NOT NULL,
    placeholder_map JSONB DEFAULT '{}',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reports table
CREATE TABLE IF NOT EXISTS public.reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES public.templates(id) ON DELETE CASCADE NOT NULL,
    data JSONB NOT NULL DEFAULT '{}',
    generated_doc_url TEXT,
    generated_pdf_url TEXT,
    signature_url TEXT,
    status report_status DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Signatures table (for tracking signature history)
CREATE TABLE IF NOT EXISTS public.signatures (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    report_id UUID REFERENCES public.reports(id) ON DELETE CASCADE NOT NULL,
    image_url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_templates_user_id ON public.templates(user_id);
CREATE INDEX IF NOT EXISTS idx_templates_type ON public.templates(type);
CREATE INDEX IF NOT EXISTS idx_templates_created_at ON public.templates(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_reports_user_id ON public.reports(user_id);
CREATE INDEX IF NOT EXISTS idx_reports_template_id ON public.reports(template_id);
CREATE INDEX IF NOT EXISTS idx_reports_status ON public.reports(status);
CREATE INDEX IF NOT EXISTS idx_reports_created_at ON public.reports(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_signatures_report_id ON public.signatures(report_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER trigger_templates_updated_at
    BEFORE UPDATE ON public.templates
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER trigger_reports_updated_at
    BEFORE UPDATE ON public.reports
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.signatures ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for templates table
CREATE POLICY "Users can view own templates" ON public.templates
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own templates" ON public.templates
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own templates" ON public.templates
    FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can delete own templates" ON public.templates
    FOR DELETE USING (auth.uid() = user_id OR user_id IS NULL);

-- RLS Policies for reports table
CREATE POLICY "Users can view own reports" ON public.reports
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own reports" ON public.reports
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own reports" ON public.reports
    FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can delete own reports" ON public.reports
    FOR DELETE USING (auth.uid() = user_id OR user_id IS NULL);

-- RLS Policies for signatures table
CREATE POLICY "Users can view signatures for own reports" ON public.signatures
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reports 
            WHERE reports.id = signatures.report_id 
            AND (reports.user_id = auth.uid() OR reports.user_id IS NULL)
        )
    );

CREATE POLICY "Users can insert signatures for own reports" ON public.signatures
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.reports 
            WHERE reports.id = signatures.report_id 
            AND (reports.user_id = auth.uid() OR reports.user_id IS NULL)
        )
    );

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('templates', 'templates', true),
    ('generated-reports', 'generated-reports', true),
    ('signatures', 'signatures', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for templates bucket
CREATE POLICY "Anyone can view templates" ON storage.objects
    FOR SELECT USING (bucket_id = 'templates');

CREATE POLICY "Authenticated users can upload templates" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'templates' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update own templates" ON storage.objects
    FOR UPDATE USING (bucket_id = 'templates' AND auth.role() = 'authenticated');

CREATE POLICY "Users can delete own templates" ON storage.objects
    FOR DELETE USING (bucket_id = 'templates' AND auth.role() = 'authenticated');

-- Storage policies for generated-reports bucket
CREATE POLICY "Anyone can view generated reports" ON storage.objects
    FOR SELECT USING (bucket_id = 'generated-reports');

CREATE POLICY "Service role can manage generated reports" ON storage.objects
    FOR ALL USING (bucket_id = 'generated-reports' AND auth.role() = 'service_role');

-- Storage policies for signatures bucket
CREATE POLICY "Anyone can view signatures" ON storage.objects
    FOR SELECT USING (bucket_id = 'signatures');

CREATE POLICY "Authenticated users can upload signatures" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'signatures' AND auth.role() = 'authenticated');

CREATE POLICY "Service role can manage signatures" ON storage.objects
    FOR ALL USING (bucket_id = 'signatures' AND auth.role() = 'service_role');

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert sample templates for testing (optional)
INSERT INTO public.templates (name, type, file_url, placeholder_map, description) VALUES
(
    'Standard Joint Report Template',
    'joint_report',
    'https://lexnztbclzychlvrauhq.supabase.co/storage/v1/object/public/templates/sample-joint-report.docx',
    '{
        "Date": "Report Date",
        "Loco_No": "Locomotive Number",
        "Train_No": "Train Number",
        "Station": "Station Name",
        "Time": "Incident Time",
        "Officer_Name": "Reporting Officer Name",
        "Designation": "Officer Designation",
        "Description": "Incident Description",
        "Signature": "Digital Signature"
    }',
    'Standard template for joint inspection reports'
),
(
    'TA Form Template',
    'ta_form',
    'https://lexnztbclzychlvrauhq.supabase.co/storage/v1/object/public/templates/sample-ta-form.docx',
    '{
        "Date": "Application Date",
        "Employee_Name": "Employee Name",
        "Employee_ID": "Employee ID",
        "Designation": "Designation",
        "From_Station": "From Station",
        "To_Station": "To Station",
        "Journey_Date": "Journey Date",
        "Purpose": "Purpose of Journey",
        "Amount": "TA Amount",
        "Signature": "Digital Signature"
    }',
    'Standard template for TA (Travel Allowance) forms'
)
ON CONFLICT DO NOTHING;
