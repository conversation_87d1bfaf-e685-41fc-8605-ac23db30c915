{"ast": null, "code": "import index from '../cjs/index.js';\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError\n} = index;\nexport { PostgrestBuilder, PostgrestClient, PostgrestFilterBuilder, PostgrestQueryBuilder, PostgrestTransformBuilder, PostgrestError };\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError\n};", "map": {"version": 3, "names": ["index", "PostgrestClient", "PostgrestQueryBuilder", "PostgrestFilterBuilder", "PostgrestTransformBuilder", "PostgrestBuilder", "PostgrestError"], "sources": ["C:/Doc Maker AI/indian-railways-reports/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs"], "sourcesContent": ["import index from '../cjs/index.js'\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = index\n\nexport {\n  PostgrestBuilder,\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n  PostgrestTransformBuilder,\n  PostgrestError,\n}\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,iBAAiB;AACnC,MAAM;EACJC,eAAe;EACfC,qBAAqB;EACrBC,sBAAsB;EACtBC,yBAAyB;EACzBC,gBAAgB;EAChBC;AACF,CAAC,GAAGN,KAAK;AAET,SACEK,gBAAgB,EAChBJ,eAAe,EACfE,sBAAsB,EACtBD,qBAAqB,EACrBE,yBAAyB,EACzBE,cAAc;;AAGhB;AACA,eAAe;EACbL,eAAe;EACfC,qBAAqB;EACrBC,sBAAsB;EACtBC,yBAAyB;EACzBC,gBAAgB;EAChBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}