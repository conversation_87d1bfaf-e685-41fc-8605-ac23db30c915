# Indian Railways Report Generator - Testing Guide

## Prerequisites

1. **Node.js** (v16 or higher)
2. **npm** or **yarn**
3. **Supabase CLI** (optional, for local development)
4. **Modern web browser** (Chrome, Firefox, Safari, Edge)

## Setup Instructions

### 1. Install Dependencies

```bash
cd indian-railways-reports
npm install
```

### 2. Environment Configuration

The `.env` file is already configured with your Supabase credentials:
- REACT_APP_SUPABASE_URL: https://lexnztbclzychlvrauhq.supabase.co
- REACT_APP_SUPABASE_ANON_KEY: [Your anon key]

### 3. Database Setup

Run the database schema in your Supabase dashboard:

1. Go to https://supabase.com/dashboard/project/lexnztbclzychlvrauhq
2. Navigate to SQL Editor
3. Copy and paste the contents of `database-schema.sql`
4. Execute the script

### 4. Deploy Edge Functions

```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref lexnztbclzychlvrauhq

# Deploy functions
supabase functions deploy generate-report
supabase functions deploy templates
supabase functions deploy reports
supabase functions deploy upload-signature
```

## Running the Application

### Development Mode

```bash
npm start
```

The application will open at http://localhost:3000

### Production Build

```bash
npm run build
npm run preview
```

## Testing Scenarios

### 1. Template Management Testing

#### Upload Template Test
1. Navigate to "Manage Templates"
2. Click "Upload Template"
3. Fill in:
   - **Name**: "Test Joint Report"
   - **Type**: "Joint Report"
   - **File**: Upload a .docx file with placeholders like {{Date}}, {{Loco_No}}, etc.
4. Click "Upload Template"
5. Verify template appears in the list

#### Sample Template Data
Create a Word document with these placeholders:
```
INDIAN RAILWAYS - JOINT INSPECTION REPORT

Date: {{Date}}
Locomotive Number: {{Loco_No}}
Train Number: {{Train_No}}
Station: {{Station}}
Time of Incident: {{Time}}

Reporting Officer: {{Officer_Name}}
Designation: {{Designation}}

Description: {{Description}}

Signature: {{Signature}}
```

### 2. Report Creation Testing

#### Create New Report Test
1. Click "Create New Report"
2. Select a template
3. Fill in the form with sample data:
   - **Date**: 2024-01-15
   - **Locomotive Number**: WAP-7 30343
   - **Train Number**: 12951
   - **Station**: New Delhi
   - **Time**: 14:30
   - **Officer Name**: Rajesh Kumar
   - **Designation**: Assistant Loco Pilot
   - **Description**: Routine inspection completed successfully

#### Signature Capture Test
1. After filling the form, proceed to signature
2. Draw a signature in the canvas
3. Click "Save Signature"
4. Verify signature is captured

#### Document Generation Test
1. After signature, click "Generate Report"
2. Wait for processing
3. Verify PDF is generated and downloadable

### 3. Dashboard Testing

#### View Reports Test
1. Navigate to Dashboard
2. Verify all created reports are listed
3. Check status badges (Draft/Generated)
4. Test "View" and "Download" links

### 4. End-to-End Testing

#### Complete Workflow Test
1. Upload a template
2. Create a report using that template
3. Fill all required fields
4. Add signature
5. Generate document
6. Download and verify PDF content
7. Check that placeholders are replaced with actual data

## Sample Test Data

### Joint Report Sample Data
```json
{
  "Date": "2024-01-15",
  "Loco_No": "WAP-7 30343",
  "Train_No": "12951 Rajdhani Express",
  "Station": "New Delhi Junction",
  "Time": "14:30",
  "Officer_Name": "Rajesh Kumar Singh",
  "Designation": "Assistant Loco Pilot",
  "Description": "Routine pre-departure inspection completed. All systems functioning normally. No defects observed."
}
```

### TA Form Sample Data
```json
{
  "Date": "2024-01-15",
  "Employee_Name": "Priya Sharma",
  "Employee_ID": "IR123456",
  "Designation": "Station Master",
  "From_Station": "Mumbai Central",
  "To_Station": "Ahmedabad Junction",
  "Journey_Date": "2024-01-20",
  "Purpose": "Official training program",
  "Amount": "₹2,500"
}
```

## Troubleshooting

### Common Issues

1. **Template Upload Fails**
   - Check file format (.docx only)
   - Verify file size (max 10MB)
   - Check Supabase storage permissions

2. **Document Generation Fails**
   - Verify Edge Functions are deployed
   - Check browser console for errors
   - Ensure all required fields are filled

3. **Signature Not Saving**
   - Check browser permissions
   - Verify storage bucket permissions
   - Try clearing browser cache

4. **PDF Not Generating**
   - Check Edge Function logs in Supabase
   - Verify pdf-lib dependency is working
   - Check network connectivity

### Debug Steps

1. **Check Browser Console**
   - Open Developer Tools (F12)
   - Look for JavaScript errors
   - Check Network tab for failed requests

2. **Check Supabase Logs**
   - Go to Supabase Dashboard
   - Navigate to Edge Functions
   - Check function logs for errors

3. **Verify Database**
   - Check if tables are created correctly
   - Verify RLS policies are active
   - Check data insertion in tables

## Performance Testing

### Load Testing
1. Upload multiple templates
2. Create multiple reports simultaneously
3. Generate multiple documents
4. Monitor response times

### Browser Compatibility
Test on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Security Testing

1. **File Upload Security**
   - Try uploading non-.docx files
   - Test with large files
   - Verify file type validation

2. **Data Validation**
   - Test with empty fields
   - Test with special characters
   - Test with very long text

3. **Authentication Testing**
   - Test without authentication (should fail)
   - Test with expired tokens
   - Verify RLS policies work correctly

## Success Criteria

✅ Templates can be uploaded successfully
✅ Reports can be created with all field types
✅ Signatures can be captured and saved
✅ Documents are generated with correct data
✅ PDFs are downloadable and readable
✅ All placeholders are replaced correctly
✅ Application works on mobile devices
✅ Error handling works properly
✅ Performance is acceptable (< 5s for document generation)

## Next Steps After Testing

1. **Deploy to Production**
   - Use deployment guide
   - Configure production environment variables
   - Test in production environment

2. **Add Authentication**
   - Implement Supabase Auth
   - Add login/signup flows
   - Test multi-user scenarios

3. **Enhance Features**
   - Add more template types
   - Implement template sharing
   - Add report analytics
   - Implement bulk operations
