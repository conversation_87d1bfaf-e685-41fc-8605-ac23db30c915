{"ast": null, "code": "export class StorageError extends Error {\n  constructor(message) {\n    super(message);\n    this.__isStorageError = true;\n    this.name = 'StorageError';\n  }\n}\nexport function isStorageError(error) {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n  constructor(message, status, statusCode) {\n    super(message);\n    this.name = 'StorageApiError';\n    this.status = status;\n    this.statusCode = statusCode;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      statusCode: this.statusCode\n    };\n  }\n}\nexport class StorageUnknownError extends StorageError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'StorageUnknownError';\n    this.originalError = originalError;\n  }\n}", "map": {"version": 3, "names": ["StorageError", "Error", "constructor", "message", "__isStorageError", "name", "isStorageError", "error", "StorageApiError", "status", "statusCode", "toJSON", "StorageUnknownError", "originalError"], "sources": ["C:\\Doc Maker AI\\indian-railways-reports\\node_modules\\@supabase\\storage-js\\src\\lib\\errors.ts"], "sourcesContent": ["export class StorageError extends Error {\n  protected __isStorageError = true\n\n  constructor(message: string) {\n    super(message)\n    this.name = 'StorageError'\n  }\n}\n\nexport function isStorageError(error: unknown): error is StorageError {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error\n}\n\nexport class StorageApiError extends StorageError {\n  status: number\n  statusCode: string\n\n  constructor(message: string, status: number, statusCode: string) {\n    super(message)\n    this.name = 'StorageApiError'\n    this.status = status\n    this.statusCode = statusCode\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      statusCode: this.statusCode,\n    }\n  }\n}\n\nexport class StorageUnknownError extends StorageError {\n  originalError: unknown\n\n  constructor(message: string, originalError: unknown) {\n    super(message)\n    this.name = 'StorageUnknownError'\n    this.originalError = originalError\n  }\n}\n"], "mappings": "AAAA,OAAM,MAAOA,YAAa,SAAQC,KAAK;EAGrCC,YAAYC,OAAe;IACzB,KAAK,CAACA,OAAO,CAAC;IAHN,KAAAC,gBAAgB,GAAG,IAAI;IAI/B,IAAI,CAACC,IAAI,GAAG,cAAc;EAC5B;;AAGF,OAAM,SAAUC,cAAcA,CAACC,KAAc;EAC3C,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,kBAAkB,IAAIA,KAAK;AACnF;AAEA,OAAM,MAAOC,eAAgB,SAAQR,YAAY;EAI/CE,YAAYC,OAAe,EAAEM,MAAc,EAAEC,UAAkB;IAC7D,KAAK,CAACP,OAAO,CAAC;IACd,IAAI,CAACE,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAEAC,MAAMA,CAAA;IACJ,OAAO;MACLN,IAAI,EAAE,IAAI,CAACA,IAAI;MACfF,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBM,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,UAAU,EAAE,IAAI,CAACA;KAClB;EACH;;AAGF,OAAM,MAAOE,mBAAoB,SAAQZ,YAAY;EAGnDE,YAAYC,OAAe,EAAEU,aAAsB;IACjD,KAAK,CAACV,OAAO,CAAC;IACd,IAAI,CAACE,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACQ,aAAa,GAAGA,aAAa;EACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}