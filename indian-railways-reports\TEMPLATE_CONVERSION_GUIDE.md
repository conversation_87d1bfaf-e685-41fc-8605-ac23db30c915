# Template Conversion Guide - Indian Railways Report Generator

This guide helps you convert your existing Indian Railways templates to work with the report generator system.

## Template Requirements

### Supported Formats
- **.docx files only** (Microsoft Word 2007+)
- Maximum file size: 10MB
- Must contain placeholder text for dynamic content

### Placeholder Format
Use double curly braces for placeholders: `{{PlaceholderName}}`

Example: `{{Date}}`, `{{Officer_Name}}`, `{{Signature}}`

## Converting Your Templates

### Step 1: Identify Dynamic Content

Review your existing templates and identify content that changes for each report:
- Dates and times
- Names and designations
- Numbers (locomotive, train, employee ID)
- Descriptions and remarks
- Signatures

### Step 2: Replace with Placeholders

Replace dynamic content with standardized placeholders:

#### Common Placeholders for Joint Reports
```
{{Date}} - Report date
{{Time}} - Time of incident/inspection
{{Loco_No}} - Locomotive number
{{Train_No}} - Train number
{{Station}} - Station name
{{Officer_Name}} - Reporting officer name
{{Designation}} - Officer designation
{{Description}} - Incident/inspection description
{{Signature}} - Digital signature placeholder
```

#### Common Placeholders for TA Forms
```
{{Date}} - Application date
{{Employee_Name}} - Employee full name
{{Employee_ID}} - Employee ID number
{{Designation}} - Employee designation
{{From_Station}} - Origin station
{{To_Station}} - Destination station
{{Journey_Date}} - Date of journey
{{Purpose}} - Purpose of travel
{{Amount}} - TA amount
{{Signature}} - Digital signature placeholder
```

### Step 3: Template Structure

#### Header Section
```
INDIAN RAILWAYS
[Division/Zone Name]

JOINT INSPECTION REPORT / TA FORM

Date: {{Date}}
Time: {{Time}}
```

#### Body Section
```
Locomotive Number: {{Loco_No}}
Train Number: {{Train_No}}
Station: {{Station}}

Reporting Officer: {{Officer_Name}}
Designation: {{Designation}}

Details:
{{Description}}
```

#### Footer Section
```
Officer Signature: {{Signature}}

Date: {{Date}}
```

## Sample Templates

### Joint Report Template

Create a Word document with this content:

```
भारतीय रेल / INDIAN RAILWAYS
[Your Division Name]

संयुक्त निरीक्षण रिपोर्ट / JOINT INSPECTION REPORT

दिनांक / Date: {{Date}}
समय / Time: {{Time}}

लोको संख्या / Locomotive Number: {{Loco_No}}
ट्रेन संख्या / Train Number: {{Train_No}}
स्टेशन / Station: {{Station}}

रिपोर्टिंग अधिकारी / Reporting Officer: {{Officer_Name}}
पदनाम / Designation: {{Designation}}

विवरण / Description:
{{Description}}

निरीक्षण परिणाम / Inspection Results:
- सभी सिस्टम सामान्य / All systems normal
- कोई दोष नहीं मिला / No defects found
- अनुशंसाएं / Recommendations: {{Recommendations}}

अधिकारी हस्ताक्षर / Officer Signature:
{{Signature}}

दिनांक / Date: {{Date}}
```

### TA Form Template

Create a Word document with this content:

```
भारतीय रेल / INDIAN RAILWAYS
[Your Division Name]

यात्रा भत्ता फॉर्म / TRAVEL ALLOWANCE FORM

आवेदन दिनांक / Application Date: {{Date}}

कर्मचारी विवरण / Employee Details:
नाम / Name: {{Employee_Name}}
कर्मचारी संख्या / Employee ID: {{Employee_ID}}
पदनाम / Designation: {{Designation}}

यात्रा विवरण / Journey Details:
प्रस्थान स्टेशन / From Station: {{From_Station}}
गंतव्य स्टेशन / To Station: {{To_Station}}
यात्रा दिनांक / Journey Date: {{Journey_Date}}
उद्देश्य / Purpose: {{Purpose}}

वित्तीय विवरण / Financial Details:
यात्रा भत्ता राशि / TA Amount: {{Amount}}

कर्मचारी हस्ताक्षर / Employee Signature:
{{Signature}}

दिनांक / Date: {{Date}}
```

## Placeholder Mapping

When uploading templates, the system will automatically detect placeholders and create a mapping. You can customize this mapping:

### Example Mapping for Joint Report
```json
{
  "Date": "Report Date",
  "Time": "Incident Time", 
  "Loco_No": "Locomotive Number",
  "Train_No": "Train Number",
  "Station": "Station Name",
  "Officer_Name": "Reporting Officer Name",
  "Designation": "Officer Designation",
  "Description": "Incident Description",
  "Recommendations": "Recommendations",
  "Signature": "Digital Signature"
}
```

### Example Mapping for TA Form
```json
{
  "Date": "Application Date",
  "Employee_Name": "Employee Name",
  "Employee_ID": "Employee ID",
  "Designation": "Designation",
  "From_Station": "From Station",
  "To_Station": "To Station", 
  "Journey_Date": "Journey Date",
  "Purpose": "Purpose of Travel",
  "Amount": "TA Amount",
  "Signature": "Digital Signature"
}
```

## Best Practices

### 1. Consistent Naming
- Use consistent placeholder names across templates
- Use descriptive names (e.g., `{{Officer_Name}}` not `{{Name}}`)
- Avoid spaces in placeholder names

### 2. Formatting
- Keep original document formatting
- Placeholders will inherit surrounding text formatting
- Use tables for structured data

### 3. Signature Placement
- Always include `{{Signature}}` placeholder
- Leave adequate space for signature image
- Consider signature size (typically 150x75 pixels)

### 4. Testing
- Test templates with sample data
- Verify all placeholders are replaced
- Check document formatting after generation

## Validation Checklist

Before uploading your template:

- [ ] File is in .docx format
- [ ] File size is under 10MB
- [ ] All dynamic content replaced with placeholders
- [ ] Placeholder names follow naming conventions
- [ ] `{{Signature}}` placeholder included
- [ ] Document formatting preserved
- [ ] Template tested with sample data

## Common Issues and Solutions

### Issue: Placeholder not replaced
**Solution**: Check placeholder spelling and ensure it matches exactly (case-sensitive)

### Issue: Formatting lost
**Solution**: Ensure placeholders are within properly formatted text, not in text boxes

### Issue: Signature not appearing
**Solution**: Verify `{{Signature}}` placeholder exists and has adequate space

### Issue: Special characters not displaying
**Solution**: Save document with UTF-8 encoding, test with Hindi/regional text

## Advanced Features

### Conditional Content
For advanced templates, you can use conditional placeholders:
```
{{#if Emergency}}
EMERGENCY REPORT
{{/if}}
```

### Repeated Sections
For lists or repeated data:
```
{{#each Items}}
- {{Name}}: {{Description}}
{{/each}}
```

### Date Formatting
Dates can be formatted automatically:
```
{{Date}} - Default format (DD/MM/YYYY)
{{Date:DD-MM-YYYY}} - Custom format
```

## Getting Help

If you need assistance converting your templates:

1. **Check the sample templates** provided in this guide
2. **Review the placeholder mapping** in the application
3. **Test with sample data** before using in production
4. **Contact support** if you encounter issues

## Template Library

The system includes these pre-built templates:
- Standard Joint Inspection Report
- TA Form Template
- Incident Report Template
- Maintenance Report Template

You can use these as starting points for your custom templates.

Remember: The goal is to create templates that generate professional, consistent reports while maintaining the official Indian Railways format and bilingual content where required.
